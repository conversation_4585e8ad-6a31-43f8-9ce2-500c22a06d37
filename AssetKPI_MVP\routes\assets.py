"""
Asset routes for the AssetKPI application.

This module defines the API routes for asset management.
"""

import logging
from typing import Dict, List, Any, Optional, Callable

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from db_models import Asset, User

# Create a logger for this module
logger = logging.getLogger(__name__)

# Create a router for asset endpoints
assets_router = APIRouter(prefix="/api/assets", tags=["Assets"])

# Store dependencies - these will be set by init_router
_get_db = None
_get_current_user = None


def init_router(
    get_db: Callable,
    get_current_user: Callable
):
    """
    Initialize the router with dependencies.

    Args:
        get_db: Dependency to get database session
        get_current_user: Dependency to get current user
    """
    global _get_db, _get_current_user

    _get_db = get_db
    _get_current_user = get_current_user


def get_db_dep():
    """Get database dependency"""
    return _get_db()


def get_user_dep():
    """Get current user dependency"""
    return _get_current_user()


@assets_router.get("", summary="Get all assets")
async def get_assets(
    limit: int = Query(100, description="Maximum number of assets to return"),
    offset: int = Query(0, description="Number of assets to skip"),
    status: Optional[str] = Query(None, description="Filter by status"),
    asset_type: Optional[str] = Query(None, description="Filter by asset type"),
    location: Optional[str] = Query(None, description="Filter by location"),
    db: Session = Depends(get_db_dep),
    current_user: User = Depends(get_user_dep)
):
    """
    Get a list of assets with optional filtering.
    """
    try:
        # Build query
        query = db.query(Asset)

        # Apply filters
        if status:
            query = query.filter(Asset.status == status)

        if asset_type:
            query = query.filter(Asset.assettype == asset_type)

        if location:
            query = query.filter(Asset.location_id == location)

        # Apply pagination
        total_count = query.count()
        assets = query.offset(offset).limit(limit).all()

        return {
            "success": True,
            "data": assets,
            "total": total_count,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"Error fetching assets: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching assets: {str(e)}"
        )


@assets_router.get("/{asset_id}", summary="Get asset by ID")
async def get_asset(
    asset_id: int,
    db: Session = Depends(get_db_dep),
    current_user: User = Depends(get_user_dep)
):
    """
    Get a specific asset by ID.
    """
    try:
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )

        return {
            "success": True,
            "data": asset
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching asset {asset_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching asset: {str(e)}"
        )


@assets_router.post("", summary="Create a new asset")
async def create_asset(
    asset_data: dict,
    request: Request,
    db: Session = Depends(get_db_dep),
    current_user: User = Depends(get_user_dep)
):
    """
    Create a new asset.
    """
    # Check user permissions
    if current_user.role not in ["ENGINEER", "MANAGER", "ADMIN"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to create assets"
        )
    
    try:
        # Map form field names to database field names
        field_mapping = {
            'assetName': 'assetname',
            'assetType': 'assettype',
            'assetLocation': 'location_id',
            'assetStatus': 'status',
            'assetManufacturer': 'manufacturer',
            'assetModel': 'model',
            'assetSerial': 'serialnumber',
            'assetCriticality': 'criticality',
            'assetPurchaseDate': 'purchasedate',
            'assetPurchaseCost': 'purchasecost',
            'assetDescription': 'description'
        }
        
        # Map form data to database fields
        db_data = {}
        for form_field, db_field in field_mapping.items():
            if form_field in asset_data and asset_data[form_field]:
                db_data[db_field] = asset_data[form_field]
        
        # Create asset
        new_asset = Asset(**db_data)
        db.add(new_asset)
        db.commit()
        db.refresh(new_asset)
        
        # Log activity
        logger.info(f"User {current_user.email} created asset {new_asset.assetid}")
        
        return {
            "success": True,
            "message": "Asset created successfully",
            "data": new_asset
        }
    
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating asset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error creating asset"
        )
    
    except Exception as e:
        logger.error(f"Error creating asset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating asset: {str(e)}"
        )


@assets_router.put("/{asset_id}", summary="Update an asset")
async def update_asset(
    asset_id: int,
    asset_data: dict,
    db: Session = Depends(get_db_dep),
    current_user: User = Depends(get_user_dep)
):
    """
    Update an existing asset.
    """
    # Check user permissions
    if current_user.role not in ["ENGINEER", "MANAGER", "ADMIN"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to update assets"
        )
    
    try:
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )

        # Update asset fields
        for key, value in asset_data.items():
            if hasattr(asset, key) and value is not None:
                setattr(asset, key, value)

        db.commit()
        db.refresh(asset)
        
        # Log activity
        logger.info(f"User {current_user.email} updated asset {asset_id}")
        
        return {
            "success": True,
            "message": "Asset updated successfully",
            "data": asset
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating asset {asset_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating asset: {str(e)}"
        )


@assets_router.delete("/{asset_id}", summary="Delete an asset")
async def delete_asset(
    asset_id: int,
    db: Session = Depends(get_db_dep),
    current_user: User = Depends(get_user_dep)
):
    """
    Delete an asset.
    """
    # Check user permissions
    if current_user.role not in ["MANAGER", "ADMIN"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to delete assets"
        )
    
    try:
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )

        db.delete(asset)
        db.commit()
        
        # Log activity
        logger.info(f"User {current_user.email} deleted asset {asset_id}")
        
        return {
            "success": True,
            "message": "Asset deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting asset {asset_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting asset: {str(e)}"
        )
