<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AssetKPI{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="/static/css/onboarding.css" rel="stylesheet">
    <link href="/static/css/tutorials.css" rel="stylesheet">
    <link href="/static/css/help.css" rel="stylesheet">
    <link href="/static/css/forms.css" rel="stylesheet">
    <link href="/static/css/mobile.css" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    {% block styles %}{% endblock %}
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .user-info {
            display: flex;
            align-items: center;
            color: #fff;
            margin-right: 15px;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 10px;
        }
        .user-info .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #0d6efd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: white;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        .user-info .user-details {
            display: flex;
            flex-direction: column;
        }
        @media (min-width: 576px) {
            .user-info .user-details {
                flex-direction: row;
                align-items: center;
            }
        }
        #logout-button {
            background-color: transparent;
            border: 1px solid rgba(255, 255, 255, 0.5);
            color: white;
            transition: all 0.3s ease;
            padding: 5px 15px;
            font-size: 0.875rem;
        }
        #logout-button:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .auth-required, .auth-not-required {
            display: none;
        }
        .auth-required[style="display: block;"], .auth-not-required[style="display: block;"] {
            display: block !important;
        }
        @media (min-width: 992px) {
            .user-info {
                margin-bottom: 0;
            }

            .d-flex.flex-column.flex-lg-row {
                flex-direction: row !important;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">AssetKPI</a>
            <div class="d-flex d-lg-none ms-auto me-2">
                <button class="btn btn-sm btn-outline-light d-lg-none me-2" type="button" id="searchToggle">
                    <i class="bi bi-search"></i>
                </button>
                <div class="dropdown d-lg-none auth-required" data-role="ENGINEER,MANAGER,ADMIN">
                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" id="quickActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-lightning"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="quickActionsDropdown">
                        <li><a class="dropdown-item" href="/assets"><i class="bi bi-plus-circle me-2"></i>New Asset</a></li>
                        <li><a class="dropdown-item" href="/inventory"><i class="bi bi-box me-2"></i>New Inventory Item</a></li>
                    </ul>
                </div>
            </div>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Mobile search form (hidden by default) -->
                <form class="d-lg-none mt-2 mb-2 w-100 d-none" id="mobileSearchForm">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search..." aria-label="Search">
                        <button class="btn btn-outline-light" type="submit"><i class="bi bi-search"></i></button>
                    </div>
                </form>

                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-speedometer2 d-lg-none me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown auth-required" data-role="VIEWER,ENGINEER,MANAGER,ADMIN">
                        <a class="nav-link dropdown-toggle" href="#" id="assetsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-gear d-lg-none me-2"></i>Assets
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="assetsDropdown">
                            <li><a class="dropdown-item" href="/assets"><i class="bi bi-list-ul d-lg-none me-2"></i>Asset List</a></li>
                            <li><a class="dropdown-item" href="/asset-performance"><i class="bi bi-graph-up d-lg-none me-2"></i>Performance Visualization</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown auth-required" data-role="VIEWER,ENGINEER,MANAGER,ADMIN">
                        <a class="nav-link dropdown-toggle" href="#" id="kpiDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-graph-up d-lg-none me-2"></i>KPI
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="kpiDropdown">
                            <li><a class="dropdown-item" href="/kpi-dashboard"><i class="bi bi-speedometer2 d-lg-none me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="/kpi-reports"><i class="bi bi-file-text d-lg-none me-2"></i>Reports</a></li>
                            <li><a class="dropdown-item" href="/kpi-analytics"><i class="bi bi-bar-chart d-lg-none me-2"></i>Analytics</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown auth-required" data-role="ENGINEER,MANAGER,ADMIN">
                        <a class="nav-link dropdown-toggle" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-box-seam d-lg-none me-2"></i>Inventory
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="inventoryDropdown">
                            <li><a class="dropdown-item" href="/inventory"><i class="bi bi-box d-lg-none me-2"></i>Parts</a></li>
                            <li><a class="dropdown-item" href="/inventory/analysis"><i class="bi bi-clipboard-data d-lg-none me-2"></i>Analysis</a></li>
                            <li><a class="dropdown-item" href="/inventory-visualization"><i class="bi bi-pie-chart d-lg-none me-2"></i>Visualization</a></li>
                            <li><a class="dropdown-item" href="/inventory/optimization-report"><i class="bi bi-file-earmark-bar-graph d-lg-none me-2"></i>Optimization Report</a></li>
                            <li><a class="dropdown-item" href="/inventory/config/eoq"><i class="bi bi-gear d-lg-none me-2"></i>EOQ Configuration</a></li>
                            <li><a class="dropdown-item" href="/recommendations"><i class="bi bi-lightbulb d-lg-none me-2"></i>Recommendations</a></li>
                        </ul>
                    </li>
                    <li class="nav-item auth-required" data-role="ADMIN">
                        <a class="nav-link" href="/inventory-config">
                            <i class="bi bi-sliders d-lg-none me-2"></i>Inventory Config
                        </a>
                    </li>

                    <li class="nav-item dropdown auth-required" data-role="ADMIN">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-shield-lock d-lg-none me-2"></i>Admin
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="/admin"><i class="bi bi-speedometer2 d-lg-none me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="/user-permissions"><i class="bi bi-people d-lg-none me-2"></i>User Permissions</a></li>
                            <li><a class="dropdown-item" href="/usage-analytics"><i class="bi bi-activity d-lg-none me-2"></i>Usage Analytics</a></li>
                            <li><a class="dropdown-item" href="/webhooks"><i class="bi bi-link-45deg d-lg-none me-2"></i>Webhooks</a></li>
                            <li><a class="dropdown-item" href="/erp-integration"><i class="bi bi-arrow-left-right d-lg-none me-2"></i>ERP Integration</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/chart-examples"><i class="bi bi-bar-chart-line d-lg-none me-2"></i>Chart Examples</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Desktop search form -->
                <form class="d-none d-lg-flex me-3">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search..." aria-label="Search">
                        <button class="btn btn-outline-light" type="submit"><i class="bi bi-search"></i></button>
                    </div>
                </form>

                <div class="d-flex flex-column flex-lg-row align-items-start align-items-lg-center">
                    <div class="user-info auth-required mb-2 mb-lg-0 me-lg-2">
                        <div class="user-avatar" id="user-avatar"></div>
                        <div class="user-details">
                            <small id="user-email" class="d-none d-sm-inline"></small>
                            <small id="user-role" class="d-block d-sm-inline ms-sm-2"></small>
                        </div>
                    </div>
                    <div class="auth-required">
                        <button class="btn btn-outline-light btn-sm w-100" id="logout-button">Logout</button>
                    </div>
                    <div class="auth-not-required">
                        <a href="/login" class="btn btn-outline-light btn-sm w-100">Login</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile search toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchToggle = document.getElementById('searchToggle');
            const mobileSearchForm = document.getElementById('mobileSearchForm');

            if (searchToggle && mobileSearchForm) {
                searchToggle.addEventListener('click', function() {
                    mobileSearchForm.classList.toggle('d-none');
                    if (!mobileSearchForm.classList.contains('d-none')) {
                        mobileSearchForm.querySelector('input').focus();
                    }
                });
            }
        });
    </script>

    <div class="container-fluid container-lg mt-4 px-3 px-lg-4">
        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container-fluid container-lg px-3 px-lg-4">
            <div class="row">
                <div class="col-12 col-md-6 text-center text-md-start mb-2 mb-md-0">
                    <span class="text-muted">AssetKPI &copy; 2025. All rights reserved.</span>
                </div>
                <div class="col-12 col-md-6 text-center text-md-end">
                    <a href="/privacy" class="text-muted me-3">Privacy Policy</a>
                    <a href="/terms" class="text-muted me-3">Terms of Service</a>
                    <a href="/contact" class="text-muted">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <!-- Firebase Auth -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js and plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.2.0/dist/chartjs-plugin-datalabels.min.js"></script>
    <!-- PDF Generation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>

    <script>
        // Initialize Firebase
        try {
            // Define Firebase config
            const firebaseConfig = {
                apiKey: "AIzaSyBKnd8bWDBAcnQJaioZ_75JAqCPvgDHvG4",
                authDomain: "ikios-59679.firebaseapp.com",
                projectId: "ikios-59679",
                storageBucket: "ikios-59679.appspot.com",
                messagingSenderId: "1045444071940",
                appId: "1:1045444071940:web:c5e52be89b1c4fcd8457741"
            };

            // Initialize Firebase
            firebase.initializeApp(firebaseConfig);
            console.log('Firebase initialized with configuration from server');

            // Function to load scripts in sequence
            function loadScript(url, callback) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = url;
                script.onload = callback;
                document.head.appendChild(script);
            }

            // Load the authentication scripts
            loadScript('/static/js/auth_simple.js', function() {
                console.log('Firebase auth script loaded');
                loadScript('/static/js/test_auth.js', function() {
                    console.log('Test auth script loaded');
                    loadScript('/static/js/combined_auth.js', function() {
                        console.log('Combined auth script loaded');
                        // Load other scripts after auth is loaded
                        loadScript('/static/js/chart-utils.js', function() {
                            console.log('Chart utils loaded');
                            loadScript('/static/js/kpi-dashboard.js', function() {
                                console.log('KPI dashboard script loaded');
                                // Dispatch event to signal auth scripts are loaded
                                document.dispatchEvent(new Event('assetKPIScriptsLoaded'));
                            });
                        });
                    });
                });
            });

        } catch (error) {
            console.error('Error initializing Firebase:', error);
        }
    </script>

    <script>
        // DOM elements
        const userAvatar = document.getElementById('user-avatar');
        const userEmail = document.getElementById('user-email');
        const userRole = document.getElementById('user-role');
        const logoutButton = document.getElementById('logout-button');
        const authRequiredElements = document.querySelectorAll('.auth-required');
        const authNotRequiredElements = document.querySelectorAll('.auth-not-required');

        // Function to get user initials
        function getInitials(email) {
            if (!email) return '?';
            const parts = email.split('@');
            if (parts.length === 0) return '?';

            const name = parts[0];
            if (name.length === 0) return '?';

            if (name.length === 1) return name.toUpperCase();

            return name.substring(0, 2).toUpperCase();
        }

        // Function to handle logout
        async function handleLogout() {
            try {
                console.log("Logging out...");

                // Use CombinedAuth if available, otherwise fall back to AssetKPIAuth
                if (typeof CombinedAuth !== 'undefined') {
                    await CombinedAuth.signOut();
                    console.log('Logged out using CombinedAuth');
                } else if (typeof AssetKPIAuth !== 'undefined') {
                    await AssetKPIAuth.signOut();
                    console.log('Logged out using AssetKPIAuth');
                }

                // Clear all auth cookies
                document.cookie = "firebaseIdToken=; path=/; max-age=0; SameSite=Strict";
                document.cookie = "testUserToken=; path=/; max-age=0; SameSite=Strict";

                // Redirect to logout endpoint which will handle server-side cleanup
                window.location.href = "/logout";
            } catch (error) {
                console.error("Logout error:", error);
                alert("Error logging out. Please try again.");
            }
        }

        // Function to update UI based on authentication state
        function updateAuthUI(user) {
            if (user) {
                // User is authenticated
                authRequiredElements.forEach(el => {
                    el.style.display = "block";
                });
                authNotRequiredElements.forEach(el => {
                    el.style.display = "none";
                });

                // Update user info
                userEmail.textContent = user.email;
                userAvatar.textContent = getInitials(user.email);

                // Fetch user role from API
                // Use CombinedAuth if available, otherwise fall back to AssetKPIAuth
                const authClient = (typeof CombinedAuth !== 'undefined') ? CombinedAuth : AssetKPIAuth;

                authClient.authenticatedFetch("/api/users/me")
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error("Failed to fetch user info");
                    })
                    .then(data => {
                        userRole.textContent = data.role || "Unknown Role";

                        // Update navigation based on role
                        const userRoleValue = data.role || "";
                        authRequiredElements.forEach(el => {
                            const requiredRoles = (el.dataset.role || "").split(",");
                            if (requiredRoles.length === 0 || requiredRoles.includes(userRoleValue)) {
                                el.style.display = "block";
                            } else {
                                el.style.display = "none";
                            }
                        });
                    })
                    .catch(error => {
                        console.error("Error fetching user info:", error);
                        userRole.textContent = "Role: Unknown";
                    });
            } else {
                // User is not authenticated
                authRequiredElements.forEach(el => {
                    el.style.display = "none";
                });
                authNotRequiredElements.forEach(el => {
                    el.style.display = "block";
                });
            }
        }

        // Event listeners
        if (logoutButton) {
            logoutButton.addEventListener('click', handleLogout);
        }

        // Initialize auth state when auth scripts are loaded
        document.addEventListener('assetKPIScriptsLoaded', function() {
            console.log('Auth scripts loaded, initializing auth state');
            // Try to use CombinedAuth first, then fall back to AssetKPIAuth
            if (typeof CombinedAuth !== 'undefined') {
                console.log('Using CombinedAuth for initialization');
                // CombinedAuth is initialized in its own script
                // Just need to set up the UI update
                if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.initAuth) {
                    AssetKPIAuth.initAuth(updateAuthUI);
                }
            } else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.initAuth) {
                console.log('Using AssetKPIAuth for initialization');
                AssetKPIAuth.initAuth(updateAuthUI);
            } else {
                console.error('No authentication module is available after scripts loaded');
            }
        });

        // Also try to initialize after a delay as a fallback
        setTimeout(() => {
            console.log('Fallback: Initializing auth state after delay');
            if (typeof CombinedAuth !== 'undefined') {
                console.log('Using CombinedAuth for delayed initialization');
                if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.initAuth) {
                    AssetKPIAuth.initAuth(updateAuthUI);
                }
            } else if (typeof AssetKPIAuth !== 'undefined' && AssetKPIAuth.initAuth) {
                console.log('Using AssetKPIAuth for delayed initialization');
                AssetKPIAuth.initAuth(updateAuthUI);
            } else {
                console.error('No authentication module is available after delay');
            }
        }, 1000); // 1 second delay
    </script>

    <script type="module" src="/static/js/tutorials.js"></script>
    <script type="module" src="/static/js/help.js"></script>
    <script type="module" src="/static/js/mobile.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
