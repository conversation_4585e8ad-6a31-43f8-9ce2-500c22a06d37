import os
import csv
import io
import math # For sqrt in EOQ
import pandas as pd
import random
from datetime import datetime, timedelta, date, time
from datetime import datetime as datetime_class
from typing import List, Optional, Dict, Any, Tuple, Literal, Union
from contextlib import asynccontextmanager
from decimal import Decimal

# Add Firebase imports
import firebase_admin
from firebase_admin import credentials, auth

# Third-party Imports
from fastapi import (
    FastAPI, Request, Depends, File, UploadFile, Form,
    HTTPException, Path, Security, Query, status
)
from fastapi.openapi.utils import get_openapi
from fastapi.responses import HTMLResponse, JSONResponse, JSONResponse, RedirectResponse, FileResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, field_validator, ConfigDict

# Define custom rate limiter
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_429_TOO_MANY_REQUESTS
from fastapi import Request, Response
import time
from collections import defaultdict
from typing import Dict, Tuple, Optional, Callable

class RateLimiter:
    """
    A simple in-memory rate limiter for FastAPI.
    """
    def __init__(self, limit: int = 100, window: int = 60):
        """
        Initialize the rate limiter.

        Args:
            limit: Maximum number of requests allowed in the time window
            window: Time window in seconds
        """
        self.limit = limit
        self.window = window
        self.requests: Dict[str, Dict[float, int]] = defaultdict(dict)

    def _get_key(self, request: Request) -> str:
        """
        Get a unique key for the request based on client IP and optional API key.

        Args:
            request: The FastAPI request object

        Returns:
            A string key for rate limiting
        """
        # Try to get API key from header
        api_key = request.headers.get("X-API-Key", "")

        # If no API key, use client IP
        if not api_key:
            client_ip = request.client.host if request.client else "unknown"
            return f"ip:{client_ip}"

        return f"api_key:{api_key}"

    def is_rate_limited(self, request: Request) -> Tuple[bool, int, int]:
        """
        Check if a request should be rate limited.

        Args:
            request: The FastAPI request object

        Returns:
            A tuple of (is_limited, remaining, reset_time)
        """
        key = self._get_key(request)
        now = time.time()

        # Remove expired entries
        self.requests[key] = {ts: count for ts, count in self.requests[key].items()
                             if now - ts < self.window}

        # Count recent requests
        total_requests = sum(self.requests[key].values())

        # Calculate remaining requests and reset time
        remaining = max(0, self.limit - total_requests)
        reset_time = int(now + self.window)

        # Check if rate limited
        if total_requests >= self.limit:
            return True, remaining, reset_time

        # Record this request
        self.requests[key][now] = self.requests[key].get(now, 0) + 1

        return False, remaining, reset_time

class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware for rate limiting in FastAPI.
    """
    def __init__(
        self,
        app,
        default_limit: int = 100,
        default_window: int = 60,
        api_key_limit: int = 200,
        api_key_window: int = 60,
        exclude_paths: Optional[list] = None,
        get_response_headers: Optional[Callable] = None
    ):
        """
        Initialize the rate limit middleware.

        Args:
            app: The FastAPI application
            default_limit: Default request limit for regular users
            default_window: Default time window for regular users in seconds
            api_key_limit: Request limit for API key users
            api_key_window: Time window for API key users in seconds
            exclude_paths: List of paths to exclude from rate limiting
            get_response_headers: Function to get custom response headers
        """
        super().__init__(app)
        self.default_limiter = RateLimiter(default_limit, default_window)
        self.api_key_limiter = RateLimiter(api_key_limit, api_key_window)
        self.exclude_paths = exclude_paths or []
        self.get_response_headers = get_response_headers

    async def dispatch(self, request: Request, call_next):
        """
        Process the request and apply rate limiting.

        Args:
            request: The FastAPI request
            call_next: The next middleware or route handler

        Returns:
            The response
        """
        # Skip rate limiting for excluded paths
        path = request.url.path
        if any(path.startswith(excluded) for excluded in self.exclude_paths):
            return await call_next(request)

        # Determine which limiter to use based on authentication method
        if request.headers.get("X-API-Key"):
            limiter = self.api_key_limiter
        else:
            limiter = self.default_limiter

        # Check rate limit
        is_limited, remaining, reset_time = limiter.is_rate_limited(request)

        # If rate limited, return 429 response
        if is_limited:
            headers = {
                "X-RateLimit-Limit": str(limiter.limit),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(reset_time),
                "Retry-After": str(reset_time - int(time.time()))
            }

            # Add custom headers if provided
            if self.get_response_headers:
                custom_headers = self.get_response_headers(request)
                if custom_headers:
                    headers.update(custom_headers)

            return Response(
                content="Rate limit exceeded",
                status_code=HTTP_429_TOO_MANY_REQUESTS,
                headers=headers
            )

        # Process the request
        response = await call_next(request)

        # Add rate limit headers to the response
        response.headers["X-RateLimit-Limit"] = str(limiter.limit)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)

        return response
from sqlalchemy import (
    create_engine, Column, Integer, String, Numeric, Date, Boolean,
    MetaData, Table, text, Text, func, DateTime, asc, desc, ForeignKey, # Added asc, desc, ForeignKey, Boolean
    Enum as SQLAlchemyEnum, and_ # Added for UserRole enum and and_ for queries
)
from sqlalchemy.orm import sessionmaker, Session, declarative_base, relationship
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import functions as sql_functions
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from dotenv import load_dotenv

# --- Load Environment Variables ---
load_dotenv()

# --- Database Configuration ---
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:Arcanum@localhost:5432/AssetKPI")
if DATABASE_URL == "postgresql://user:password@host:port/dbname":
    print("WARNING: DATABASE_URL not found in .env file. Using placeholder.")

# --- Database Dependency ---
def get_db():
    """Database session dependency"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- API Key Setup ---
API_KEY_NAME = "X-API-KEY"
API_KEY = os.getenv("SECRET_API_KEY", "c5e52be8-9b1c-4fcd-8457-741c91ef5c85")
if API_KEY == "DEFAULT_DEV_KEY":
     print("WARNING: SECRET_API_KEY not found in .env file. Using default dev key.")

# --- Initialize Firebase Admin SDK ---
try:
    # Path to your service account key file from .env
    SERVICE_ACCOUNT_KEY_PATH = os.getenv("FIREBASE_SERVICE_ACCOUNT_KEY", "firebase-service-account.json")
    print(f"INFO:     Looking for Firebase service account key at: {SERVICE_ACCOUNT_KEY_PATH}")

    # Check if file exists and print absolute path
    if not os.path.exists(SERVICE_ACCOUNT_KEY_PATH):
        print(f"WARNING:  Firebase service account key not found at {SERVICE_ACCOUNT_KEY_PATH}")
        print(f"WARNING:  Current working directory: {os.getcwd()}")
        print(f"WARNING:  Checking for file in Firebase directory...")

        # Try alternative locations
        alt_path = os.path.join("Firebase", "firebase-service-account.json")
        if os.path.exists(alt_path):
            print(f"INFO:     Found service account key at alternative path: {alt_path}")
            SERVICE_ACCOUNT_KEY_PATH = alt_path
            cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)
        else:
            print(f"ERROR:    Service account key not found in alternative location either")
            cred = None # Indicate failure to load credentials
    else:
        print(f"INFO:     Found service account key at: {os.path.abspath(SERVICE_ACCOUNT_KEY_PATH)}")
        cred = credentials.Certificate(SERVICE_ACCOUNT_KEY_PATH)

    if cred:
        try:
            firebase_admin.initialize_app(cred)
            print("INFO:     Firebase Admin SDK initialized successfully.")
        except ValueError as e:
            # App already initialized
            print(f"INFO:     Firebase Admin SDK already initialized: {e}")
    else:
        print("ERROR:    Could not initialize Firebase Admin SDK. Credentials not loaded.")
        print("ERROR:    Authentication endpoints will fail without valid credentials.")

except Exception as e:
    print(f"ERROR:    Failed to initialize Firebase Admin SDK: {e}")
    import traceback
    traceback.print_exc()
# --- END Firebase Initialization ---

# --- Configuration Constants (Default values, will be overridden by DB values if available) ---
DEFAULT_ORDERING_COST = Decimal('50.00') # Example cost per order
DEFAULT_ANNUAL_HOLDING_COST_PERCENT = Decimal('0.15') # Example: 15% of unit cost per year
SAFETY_STOCK_SERVICE_LEVEL_Z = Decimal('1.65') # Example Z-score for 95% service level
SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR = Decimal('0.2') # Placeholder: Assumes 20% lead time variability
SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR = Decimal('0.3') # Placeholder: Assumes 30% demand variability
# --- END Constants ---

# --- Function to get configuration values from database ---
def get_config_value(db: Session, param_name: str, default_value: Any) -> Any:
    """
    Get a configuration value from the database.
    If the parameter doesn't exist, return the default value.
    """
    try:
        param = db.query(InventoryConfig).filter(InventoryConfig.parameter_name == param_name).first()
        if not param:
            return default_value

        # Convert the value based on the parameter type
        if param.parameter_type == 'decimal':
            return Decimal(param.parameter_value)
        elif param.parameter_type == 'integer':
            return int(param.parameter_value)
        elif param.parameter_type == 'boolean':
            return param.parameter_value.lower() in ('true', 'yes', '1', 't', 'y')
        else:  # string or other types
            return param.parameter_value
    except Exception as e:
        print(f"Error getting config value for {param_name}: {e}")
        return default_value
# --- END Function ---

api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

async def get_api_key(api_key: Optional[str] = Depends(api_key_header)):
    """Dependency function to validate API Key"""
    if api_key is None:
        return None
    if api_key == API_KEY:
        return api_key
    else:
        raise HTTPException(
            status_code=403,
            detail="Could not validate credentials",
        )

# --- SQLAlchemy Setup ---
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# --- Jinja2 Templates Setup ---
templates = Jinja2Templates(directory="templates")

# --- Scheduler Setup ---
scheduler = AsyncIOScheduler()

# --- Import Custom Utilities ---
# Define utility functions directly in main.py to avoid import issues

# Pagination utility functions
from typing import List, Dict, Any, Optional, TypeVar, Generic, Callable, Type, Union
from fastapi import Query, Request
from pydantic import BaseModel, Field
from sqlalchemy.orm import Query as SQLAlchemyQuery
from sqlalchemy.exc import SQLAlchemyError
from math import ceil

T = TypeVar('T')

class PaginationParams:
    """
    Common pagination parameters for list endpoints.
    """
    def __init__(
        self,
        limit: int = Query(10, ge=1, le=100, description="Number of items to return per page"),
        offset: int = Query(0, ge=0, description="Number of items to skip"),
        sort_by: Optional[str] = Query(None, description="Field to sort by"),
        sort_order: str = Query("asc", description="Sort order (asc or desc)")
    ):
        self.limit = limit
        self.offset = offset
        self.sort_by = sort_by
        self.sort_order = sort_order.lower()

    def get_sort_direction(self):
        """
        Get the SQLAlchemy sort direction based on sort_order.
        """
        from sqlalchemy import asc, desc
        return asc if self.sort_order == "asc" else desc


class PaginatedResponse(BaseModel, Generic[T]):
    """
    Standard response format for paginated results.
    """
    items: List[T]
    total: int
    limit: int
    offset: int
    has_more: bool

    # Add links for next and previous pages
    next_page: Optional[str] = None
    prev_page: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True


def paginate_query(
    query: SQLAlchemyQuery,
    pagination: PaginationParams,
    model,
    request: Request
) -> Dict[str, Any]:
    """
    Apply pagination to a SQLAlchemy query and return a paginated response.

    Args:
        query: The SQLAlchemy query to paginate
        pagination: The pagination parameters
        model: The SQLAlchemy model being queried
        request: The FastAPI request object for generating links

    Returns:
        A dictionary with pagination metadata and results
    """
    # Apply sorting if specified
    if pagination.sort_by and hasattr(model, pagination.sort_by):
        sort_column = getattr(model, pagination.sort_by)
        sort_direction = pagination.get_sort_direction()
        query = query.order_by(sort_direction(sort_column))

    # Get total count before applying limit/offset
    total = query.count()

    # Apply limit and offset
    query = query.limit(pagination.limit).offset(pagination.offset)

    # Execute query
    items = query.all()

    # Calculate if there are more items
    has_more = (pagination.offset + pagination.limit) < total

    # Generate links for next and previous pages
    base_url = str(request.url).split('?')[0]
    query_params = dict(request.query_params)

    # Next page link
    next_page = None
    if has_more:
        next_offset = pagination.offset + pagination.limit
        query_params['offset'] = str(next_offset)
        next_page = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in query_params.items()])}"

    # Previous page link
    prev_page = None
    if pagination.offset > 0:
        prev_offset = max(0, pagination.offset - pagination.limit)
        query_params['offset'] = str(prev_offset)
        prev_page = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in query_params.items()])}"

    return {
        "items": items,
        "total": total,
        "limit": pagination.limit,
        "offset": pagination.offset,
        "has_more": has_more,
        "next_page": next_page,
        "prev_page": prev_page
    }

# Filtering utility functions
class FilterOperator:
    """
    Supported filter operators.
    """
    EQ = "eq"  # Equal
    NE = "ne"  # Not equal
    GT = "gt"  # Greater than
    GE = "ge"  # Greater than or equal
    LT = "lt"  # Less than
    LE = "le"  # Less than or equal
    LIKE = "like"  # LIKE (case-sensitive)
    ILIKE = "ilike"  # ILIKE (case-insensitive)
    IN = "in"  # IN (list of values)
    NOT_IN = "not_in"  # NOT IN (list of values)
    IS_NULL = "is_null"  # IS NULL
    IS_NOT_NULL = "is_not_null"  # IS NOT NULL
    BETWEEN = "between"  # BETWEEN (two values)


class FilterCondition(BaseModel):
    """
    A single filter condition.
    """
    field: str
    operator: str = FilterOperator.EQ
    value: Optional[Any] = None

    class Config:
        arbitrary_types_allowed = True


def apply_filter_conditions(
    query: Query,
    filter_conditions: List[FilterCondition],
    model: Any
) -> Query:
    """
    Apply a list of filter conditions to a SQLAlchemy query.

    Args:
        query: The SQLAlchemy query to filter
        filter_conditions: List of filter conditions to apply
        model: The SQLAlchemy model being queried

    Returns:
        The filtered SQLAlchemy query
    """
    for condition in filter_conditions:
        if not hasattr(model, condition.field):
            # Skip conditions for fields that don't exist in the model
            continue

        column = getattr(model, condition.field)

        if condition.operator == FilterOperator.EQ:
            query = query.filter(column == condition.value)
        elif condition.operator == FilterOperator.NE:
            query = query.filter(column != condition.value)
        elif condition.operator == FilterOperator.GT:
            query = query.filter(column > condition.value)
        elif condition.operator == FilterOperator.GE:
            query = query.filter(column >= condition.value)
        elif condition.operator == FilterOperator.LT:
            query = query.filter(column < condition.value)
        elif condition.operator == FilterOperator.LE:
            query = query.filter(column <= condition.value)
        elif condition.operator == FilterOperator.LIKE:
            query = query.filter(column.like(f"%{condition.value}%"))
        elif condition.operator == FilterOperator.ILIKE:
            query = query.filter(column.ilike(f"%{condition.value}%"))
        elif condition.operator == FilterOperator.IN:
            if isinstance(condition.value, list):
                query = query.filter(column.in_(condition.value))
        elif condition.operator == FilterOperator.NOT_IN:
            if isinstance(condition.value, list):
                query = query.filter(~column.in_(condition.value))
        elif condition.operator == FilterOperator.IS_NULL:
            query = query.filter(column.is_(None))
        elif condition.operator == FilterOperator.IS_NOT_NULL:
            query = query.filter(column.isnot(None))
        elif condition.operator == FilterOperator.BETWEEN:
            if isinstance(condition.value, list) and len(condition.value) == 2:
                query = query.filter(column.between(condition.value[0], condition.value[1]))

    return query


def parse_filter_params(filter_params: Dict[str, Any]) -> List[FilterCondition]:
    """
    Parse filter parameters from query parameters.

    The format is:
    field=value (simple equality)
    field__operator=value (with operator)

    Examples:
    - status=OPEN
    - created_at__gt=2023-01-01
    - price__between=10,20

    Args:
        filter_params: Dictionary of query parameters

    Returns:
        List of FilterCondition objects
    """
    conditions = []

    for key, value in filter_params.items():
        if "__" in key:
            field, operator = key.split("__", 1)
        else:
            field, operator = key, FilterOperator.EQ

        # Handle special value formats
        if operator == FilterOperator.BETWEEN and isinstance(value, str):
            value = value.split(",")
            if len(value) == 2:
                try:
                    value = [float(value[0]), float(value[1])]
                except ValueError:
                    # If not numeric, keep as strings
                    pass
        elif operator == FilterOperator.IN or operator == FilterOperator.NOT_IN:
            if isinstance(value, str):
                value = value.split(",")

        conditions.append(FilterCondition(field=field, operator=operator, value=value))

    return conditions


def apply_filters_from_params(
    query: Query,
    filter_params: Dict[str, Any],
    model: Any
) -> Query:
    """
    Apply filters from query parameters to a SQLAlchemy query.

    Args:
        query: The SQLAlchemy query to filter
        filter_params: Dictionary of query parameters
        model: The SQLAlchemy model being queried

    Returns:
        The filtered SQLAlchemy query
    """
    conditions = parse_filter_params(filter_params)
    return apply_filter_conditions(query, conditions, model)

# Bulk operations utility functions
def bulk_create(
    db: Session,
    model_class: Type,
    items: List[Dict[str, Any]],
    return_instances: bool = False
) -> Union[List[Dict[str, Any]], List[Any]]:
    """
    Bulk create multiple items in the database.

    Args:
        db: SQLAlchemy database session
        model_class: SQLAlchemy model class
        items: List of dictionaries with item data
        return_instances: Whether to return the created model instances

    Returns:
        List of created items (as dictionaries or model instances)

    Raises:
        HTTPException: If there's an error during the operation
    """
    try:
        # Create model instances
        instances = [model_class(**item) for item in items]

        # Add all instances to the session
        db.add_all(instances)
        db.commit()

        # Refresh instances to get generated IDs
        for instance in instances:
            db.refresh(instance)

        if return_instances:
            return instances

        # Convert instances to dictionaries
        result = []
        for instance in instances:
            item_dict = {}
            for column in instance.__table__.columns:
                item_dict[column.name] = getattr(instance, column.name)
            result.append(item_dict)

        return result

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error during bulk create: {str(e)}"
        )


def bulk_update(
    db: Session,
    model_class: Type,
    items: List[Dict[str, Any]],
    id_field: str = "id",
    return_instances: bool = False
) -> Union[List[Dict[str, Any]], List[Any]]:
    """
    Bulk update multiple items in the database.

    Args:
        db: SQLAlchemy database session
        model_class: SQLAlchemy model class
        items: List of dictionaries with item data (must include ID field)
        id_field: Name of the ID field
        return_instances: Whether to return the updated model instances

    Returns:
        List of updated items (as dictionaries or model instances)

    Raises:
        HTTPException: If there's an error during the operation
    """
    try:
        # Get all IDs
        ids = [item[id_field] for item in items if id_field in item]

        # Fetch existing instances
        instances = db.query(model_class).filter(
            getattr(model_class, id_field).in_(ids)
        ).all()

        # Create a mapping of ID to instance
        instance_map = {getattr(instance, id_field): instance for instance in instances}

        # Update instances
        updated_instances = []
        for item in items:
            if id_field not in item:
                continue

            instance_id = item[id_field]
            if instance_id in instance_map:
                instance = instance_map[instance_id]

                # Update attributes
                for key, value in item.items():
                    if key != id_field and hasattr(instance, key):
                        setattr(instance, key, value)

                updated_instances.append(instance)

        # Commit changes
        db.commit()

        # Refresh instances
        for instance in updated_instances:
            db.refresh(instance)

        if return_instances:
            return updated_instances

        # Convert instances to dictionaries
        result = []
        for instance in updated_instances:
            item_dict = {}
            for column in instance.__table__.columns:
                item_dict[column.name] = getattr(instance, column.name)
            result.append(item_dict)

        return result

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error during bulk update: {str(e)}"
        )


def bulk_delete(
    db: Session,
    model_class: Type,
    ids: List[Any],
    id_field: str = "id"
) -> Dict[str, Any]:
    """
    Bulk delete multiple items from the database.

    Args:
        db: SQLAlchemy database session
        model_class: SQLAlchemy model class
        ids: List of IDs to delete
        id_field: Name of the ID field

    Returns:
        Dictionary with deletion results

    Raises:
        HTTPException: If there's an error during the operation
    """
    try:
        # Delete instances
        result = db.query(model_class).filter(
            getattr(model_class, id_field).in_(ids)
        ).delete(synchronize_session=False)

        # Commit changes
        db.commit()

        return {
            "deleted_count": result,
            "ids": ids
        }

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error during bulk delete: {str(e)}"
        )

# Bulk operations request/response models
class BulkCreateRequest(BaseModel):
    """
    Request model for bulk create operations.
    """
    items: List[Dict[str, Any]] = Field(..., description="List of items to create")


class BulkUpdateRequest(BaseModel):
    """
    Request model for bulk update operations.
    """
    items: List[Dict[str, Any]] = Field(..., description="List of items to update")


class BulkDeleteRequest(BaseModel):
    """
    Request model for bulk delete operations.
    """
    ids: List[Any] = Field(..., description="List of IDs to delete")


class BulkOperationResponse(BaseModel):
    """
    Response model for bulk operations.
    """
    status: str = Field(..., description="Operation status")
    message: str = Field(..., description="Operation message")
    success_count: int = Field(..., description="Number of successful operations")
    error_count: int = Field(0, description="Number of failed operations")
    errors: Optional[List[Dict[str, Any]]] = Field(None, description="List of errors")

# --- SQLAlchemy Database Models ---

# Define an Enum for Roles for better type safety and consistency
import enum
import random
class UserRole(str, enum.Enum):
    VIEWER = "VIEWER"
    ENGINEER = "ENGINEER"
    MANAGER = "MANAGER"
    ADMIN = "ADMIN"

class UserPermission(Base):
    __tablename__ = 'user_permissions'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), ForeignKey('users.user_id'), nullable=False, index=True)
    permission = Column(String(50), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=sql_functions.now())
    updated_at = Column(DateTime(timezone=True), server_default=sql_functions.now(), onupdate=sql_functions.now())

    def __repr__(self):
        return f"<UserPermission(user_id='{self.user_id}', permission='{self.permission}')>"

class User(Base):
    __tablename__ = 'users'
    user_id = Column(String(255), primary_key=True, index=True)
    email = Column(String(255), nullable=False, unique=True, index=True)
    role = Column(SQLAlchemyEnum(UserRole), nullable=False, default=UserRole.VIEWER, index=True)
    full_name = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=sql_functions.now())
    last_login = Column(DateTime(timezone=True))
    # New fields for API access
    api_access_enabled = Column(Boolean, default=True)
    api_key_id = Column(String(255), unique=True, index=True, nullable=True)

    # Relationships
    webhook_subscriptions = relationship("WebhookSubscription", back_populates="user")

    def __repr__(self):
        return f"<User(user_id='{self.user_id}', email='{self.email}', role='{self.role}')>"

class KpiReport(Base):
    __tablename__ = 'kpireports'; reportid = Column(Integer, primary_key=True, index=True); reportdate = Column(Date); totalworkorders = Column(Integer); preventiveratio = Column(Numeric(5, 2)); avgdowntime = Column(Numeric(10, 2)); mttr = Column(Numeric(10, 2)); mtbf = Column(Numeric(10, 2)); sparepartusage = Column(Numeric(10, 2)); maintenancecost = Column(Numeric(10, 2)); openworkorders = Column(Integer); closedworkorders = Column(Integer); oee = Column(Numeric(5, 2))
class Sparepart(Base):
    __tablename__ = 'spareparts'
    partid = Column(Integer, primary_key=True, index=True)
    partname = Column(String(100))
    partnumber = Column(String(50))
    manufacturer = Column(String(100))
    stockquantity = Column(Integer)
    monthlydemand = Column(Integer)
    reorderlevel = Column(Integer)
    safetystock = Column(Integer)
    eoq = Column(Numeric(10, 2))
    leadtimedays = Column(Integer)
    unitprice = Column(Numeric(10, 2))
    stockoutrisk = Column(Numeric(5, 2))
    lastrestocked = Column(Date)
    avg_monthly_consumption = Column(Numeric(10, 2), default=0.0)
    abc_classification = Column(String(1), index=True)
    inventory_metrics_last_updated = Column(DateTime(timezone=True))
    calculated_safety_stock = Column(Numeric(10, 2))
    # Enhanced EOQ fields
    annual_demand = Column(Numeric(12, 2))
    ordering_cost = Column(Numeric(10, 2))
    holding_cost_percent = Column(Numeric(5, 2))
    # Enhanced Safety Stock fields
    lead_time_variability = Column(Numeric(5, 2))
    demand_variability = Column(Numeric(5, 2))
    service_level = Column(Numeric(5, 2))
    reorder_point = Column(Numeric(10, 2))
    # Inventory optimization fields
    min_order_quantity = Column(Integer)
    max_order_quantity = Column(Integer)
    last_usage_date = Column(Date)
    days_of_supply = Column(Integer)
    storage_location_id = Column(Integer, ForeignKey('storage_locations.location_id'))
class WorkOrder(Base):
    __tablename__ = 'workorders'; workorderid = Column(Integer, primary_key=True, index=True); assetid = Column(Integer); workordertype = Column(String(30)); description = Column(Text); status = Column(String(30)); assignedto = Column(String(100)); failurecode = Column(String(50)); failuretype = Column(String(50)); downtimeminutes = Column(Integer); repairtimeminutes = Column(Integer); maintenancecost = Column(Numeric(10, 2)); startdate = Column(DateTime); enddate = Column(DateTime)
class WorkOrderParts(Base): # Assuming structure, adjust if needed
    __tablename__ = 'workorderparts'; workorderpartid = Column(Integer, primary_key=True, index=True); workorderid = Column(Integer); partid = Column(Integer); quantityused = Column(Integer)
class KPI(Base):
    __tablename__ = 'calculated_kpi_history'; id = Column(Integer, primary_key=True, index=True); calculation_timestamp = Column(DateTime, server_default=sql_functions.now()); kpi_name = Column(String(100), nullable=False); kpi_value = Column(Numeric(15, 5)); kpi_unit = Column(String(50)); asset_id = Column(Integer, nullable=True); calculation_source = Column(String(50))

# Alias for backward compatibility
CalculatedKpiHistory = KPI
class InventoryRecommendation(Base):
    __tablename__ = 'inventory_recommendations'; id = Column(Integer, primary_key=True, index=True); part_id = Column(Integer); recommendation_type = Column(String(50), nullable=False); reason = Column(Text); priority = Column(Integer, default=3); status = Column(String(20), default='NEW', index=True); generated_at = Column(DateTime, server_default=sql_functions.now()); last_updated_at = Column(DateTime, server_default=sql_functions.now(), onupdate=sql_functions.now())

class EOQCalculation(Base):
    __tablename__ = 'eoq_calculations'
    id = Column(Integer, primary_key=True, index=True)
    part_id = Column(Integer, ForeignKey('spareparts.partid'), nullable=False)
    annual_demand = Column(Numeric(12, 2))
    ordering_cost = Column(Numeric(10, 2))
    holding_cost = Column(Numeric(10, 2))
    eoq_value = Column(Numeric(10, 2))
    annual_ordering_cost = Column(Numeric(12, 2))
    annual_holding_cost = Column(Numeric(12, 2))
    total_annual_cost = Column(Numeric(12, 2))
    optimal_order_frequency = Column(Integer)  # Number of orders per year
    calculated_at = Column(DateTime, default=datetime.now)

class SafetyStockCalculation(Base):
    __tablename__ = 'safety_stock_calculations'
    id = Column(Integer, primary_key=True, index=True)
    part_id = Column(Integer, ForeignKey('spareparts.partid'), nullable=False)
    avg_daily_demand = Column(Numeric(10, 2))
    lead_time_days = Column(Integer)
    demand_variability = Column(Numeric(5, 2))
    lead_time_variability = Column(Numeric(5, 2))
    service_level = Column(Numeric(5, 2))
    service_level_z = Column(Numeric(5, 2))
    safety_stock_value = Column(Numeric(10, 2))
    reorder_point = Column(Numeric(10, 2))
    calculated_at = Column(DateTime, default=datetime.now)

class InventoryAnalysis(Base):
    __tablename__ = 'inventory_analysis'
    id = Column(Integer, primary_key=True, index=True)
    part_id = Column(Integer, ForeignKey('spareparts.partid'), nullable=False)
    current_stock = Column(Integer)
    optimal_stock = Column(Integer)
    stock_difference = Column(Integer)
    current_cost = Column(Numeric(12, 2))
    optimal_cost = Column(Numeric(12, 2))
    potential_savings = Column(Numeric(12, 2))
    days_of_supply = Column(Integer)
    stockout_risk = Column(Numeric(5, 2))
    last_usage_date = Column(Date)
    analysis_date = Column(DateTime, default=datetime.now)

class InventoryConfig(Base):
    __tablename__ = 'inventory_config'
    id = Column(Integer, primary_key=True, index=True)
    parameter_name = Column(String(100), nullable=False, unique=True, index=True)
    parameter_value = Column(String(255), nullable=False)
    parameter_description = Column(Text)
    parameter_type = Column(String(50), nullable=False)  # decimal, integer, string, etc.
    created_at = Column(DateTime, server_default=sql_functions.now())
    updated_at = Column(DateTime, server_default=sql_functions.now(), onupdate=sql_functions.now())

# --- Asset Hierarchy and Classification Models ---
class AssetLocation(Base):
    __tablename__ = 'asset_locations'
    location_id = Column(Integer, primary_key=True, index=True)
    location_name = Column(String(100), nullable=False)
    location_type = Column(String(50))
    parent_location_id = Column(Integer, ForeignKey('asset_locations.location_id'))
    description = Column(Text)

class AssetSystem(Base):
    __tablename__ = 'asset_systems'
    system_id = Column(Integer, primary_key=True, index=True)
    system_name = Column(String(100), nullable=False)
    description = Column(Text)
    parent_system_id = Column(Integer, ForeignKey('asset_systems.system_id'))

class AssetCategory(Base):
    __tablename__ = 'asset_categories'
    category_id = Column(Integer, primary_key=True, index=True)
    category_name = Column(String(100), nullable=False)
    description = Column(Text)
    parent_category_id = Column(Integer, ForeignKey('asset_categories.category_id'))

class Asset(Base):
    __tablename__ = 'assets'
    assetid = Column(Integer, primary_key=True, index=True)
    assetname = Column(String(100))
    assettype = Column(String(50))
    serialnumber = Column(String(50))
    location = Column(String(100))
    status = Column(String(50))
    criticality = Column(String(50))
    purchasedate = Column(Date)
    lastservicedate = Column(Date)
    lifecyclestage = Column(String(50))
    manufacturer = Column(String(100))
    model = Column(String(100))
    createdat = Column(DateTime, default=datetime.now)
    # Added in Milestone 1
    location_id = Column(Integer, ForeignKey('asset_locations.location_id'))
    system_id = Column(Integer, ForeignKey('asset_systems.system_id'))
    category_id = Column(Integer, ForeignKey('asset_categories.category_id'))
    parent_asset_id = Column(Integer, ForeignKey('assets.assetid'))

# --- Asset Specifications and Warranties Models ---
class AssetSpecification(Base):
    __tablename__ = 'asset_specifications'
    spec_id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey('assets.assetid'))
    spec_name = Column(String(100), nullable=False)
    spec_value = Column(String(255))
    spec_unit = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

class AssetWarranty(Base):
    __tablename__ = 'asset_warranties'
    warranty_id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey('assets.assetid'))
    warranty_type = Column(String(50))
    provider = Column(String(100))
    start_date = Column(Date)
    end_date = Column(Date)
    coverage_details = Column(Text)
    contact_info = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

# --- Work Order Enhancements Models ---
class WorkOrderPlan(Base):
    __tablename__ = 'work_order_plans'
    plan_id = Column(Integer, primary_key=True, index=True)
    plan_name = Column(String(100), nullable=False)
    description = Column(Text)
    estimated_duration = Column(Integer)  # in minutes
    estimated_labor_hours = Column(Numeric(10, 2))
    estimated_cost = Column(Numeric(10, 2))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

class WorkOrderTask(Base):
    __tablename__ = 'work_order_tasks'
    task_id = Column(Integer, primary_key=True, index=True)
    workorder_id = Column(Integer, ForeignKey('workorders.workorderid'))
    task_description = Column(Text, nullable=False)
    sequence_number = Column(Integer)
    estimated_hours = Column(Numeric(5, 2))
    actual_hours = Column(Numeric(5, 2))
    status = Column(String(30))
    assigned_to = Column(String(100))
    completed_by = Column(String(100))
    completed_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class LaborResource(Base):
    __tablename__ = 'labor_resources'
    resource_id = Column(Integer, primary_key=True, index=True)
    person_name = Column(String(100), nullable=False)
    employee_id = Column(String(50))
    craft = Column(String(50))
    skill_level = Column(String(50))
    labor_rate = Column(Numeric(10, 2))
    availability_status = Column(String(30))
    contact_info = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class WorkOrderLabor(Base):
    __tablename__ = 'work_order_labor'
    labor_id = Column(Integer, primary_key=True, index=True)
    workorder_id = Column(Integer, ForeignKey('workorders.workorderid'))
    labor_code = Column(String(50))
    craft = Column(String(50))
    person_id = Column(Integer, ForeignKey('labor_resources.resource_id'))
    hours_worked = Column(Numeric(10, 2))
    labor_cost = Column(Numeric(10, 2))
    work_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)

# --- Preventive Maintenance Models ---
class PMSchedule(Base):
    __tablename__ = 'pm_schedules'
    schedule_id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey('assets.assetid'))
    schedule_name = Column(String(100), nullable=False)
    frequency_type = Column(String(50), nullable=False)  # Calendar, Meter, or Condition
    frequency_value = Column(Integer)
    frequency_unit = Column(String(20))  # Days, Weeks, Months, Years, Hours, Cycles, etc.
    last_completed_date = Column(DateTime)
    next_due_date = Column(DateTime)
    enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

class PMJobPlan(Base):
    __tablename__ = 'pm_job_plans'
    plan_id = Column(Integer, primary_key=True, index=True)
    schedule_id = Column(Integer, ForeignKey('pm_schedules.schedule_id'))
    plan_name = Column(String(100), nullable=False)
    description = Column(Text)
    estimated_duration = Column(Integer)  # in minutes
    safety_instructions = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class PMJobTask(Base):
    __tablename__ = 'pm_job_tasks'
    task_id = Column(Integer, primary_key=True, index=True)
    plan_id = Column(Integer, ForeignKey('pm_job_plans.plan_id'))
    task_description = Column(Text, nullable=False)
    sequence_number = Column(Integer)
    estimated_hours = Column(Numeric(5, 2))
    required_tools = Column(Text)
    required_parts = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class AssetMeter(Base):
    __tablename__ = 'asset_meters'
    meter_id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey('assets.assetid'))
    meter_name = Column(String(100), nullable=False)
    meter_type = Column(String(50))  # Runtime, Cycles, Distance, etc.
    unit_of_measure = Column(String(50))
    current_reading = Column(Numeric(15, 5))
    last_reading_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

class MeterReading(Base):
    __tablename__ = 'meter_readings'
    reading_id = Column(Integer, primary_key=True, index=True)
    meter_id = Column(Integer, ForeignKey('asset_meters.meter_id'))
    reading_value = Column(Numeric(15, 5))
    reading_date = Column(DateTime)
    entered_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)

# --- Inventory Management Enhancements Models ---
class Storeroom(Base):
    __tablename__ = 'storerooms'
    storeroom_id = Column(Integer, primary_key=True, index=True)
    storeroom_name = Column(String(100), nullable=False)
    location = Column(String(100))
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class StorageLocation(Base):
    __tablename__ = 'storage_locations'
    location_id = Column(Integer, primary_key=True, index=True)
    storeroom_id = Column(Integer, ForeignKey('storerooms.storeroom_id'))
    location_name = Column(String(100), nullable=False)
    bin = Column(String(50))
    aisle = Column(String(50))
    shelf = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class Vendor(Base):
    __tablename__ = 'vendors'
    vendor_id = Column(Integer, primary_key=True, index=True)
    vendor_name = Column(String(100), nullable=False)
    contact_person = Column(String(100))
    phone = Column(String(50))
    email = Column(String(100))
    address = Column(Text)
    preferred = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)

# --- Purchase Orders and Inventory Transactions Models ---
class PurchaseOrder(Base):
    __tablename__ = 'purchase_orders'
    po_id = Column(Integer, primary_key=True, index=True)
    po_number = Column(String(50), unique=True, nullable=False)
    vendor_id = Column(Integer, ForeignKey('vendors.vendor_id'))
    order_date = Column(Date)
    expected_delivery_date = Column(Date)
    status = Column(String(30))
    total_amount = Column(Numeric(15, 2))
    shipping_address = Column(Text)
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

class POItem(Base):
    __tablename__ = 'po_items'
    item_id = Column(Integer, primary_key=True, index=True)
    po_id = Column(Integer, ForeignKey('purchase_orders.po_id'))
    part_id = Column(Integer, ForeignKey('spareparts.partid'))
    quantity = Column(Integer)
    unit_price = Column(Numeric(10, 2))
    line_total = Column(Numeric(15, 2))
    received_quantity = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.now)

class InventoryTransaction(Base):
    __tablename__ = 'inventory_transactions'
    transaction_id = Column(Integer, primary_key=True, index=True)
    part_id = Column(Integer, ForeignKey('spareparts.partid'))
    transaction_type = Column(String(50))  # Issue, Receipt, Return, Adjustment, Transfer
    quantity = Column(Integer)
    transaction_date = Column(DateTime)
    reference_type = Column(String(50))  # WorkOrder, PO, Adjustment
    reference_id = Column(Integer)
    location_from = Column(Integer, ForeignKey('storage_locations.location_id'))
    location_to = Column(Integer, ForeignKey('storage_locations.location_id'))
    unit_cost = Column(Numeric(10, 2))
    total_cost = Column(Numeric(15, 2))
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)

# --- Resource Management Models ---
class ResourceSkill(Base):
    __tablename__ = 'resource_skills'
    skill_id = Column(Integer, primary_key=True, index=True)
    resource_id = Column(Integer, ForeignKey('labor_resources.resource_id'))
    skill_name = Column(String(100), nullable=False)
    proficiency_level = Column(String(50))
    certification = Column(String(100))
    expiration_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)

class ToolsEquipment(Base):
    __tablename__ = 'tools_equipment'
    tool_id = Column(Integer, primary_key=True, index=True)
    tool_name = Column(String(100), nullable=False)
    tool_type = Column(String(50))
    serial_number = Column(String(50))
    status = Column(String(30))
    location = Column(String(100))
    calibration_due_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)

class WorkOrderTool(Base):
    __tablename__ = 'work_order_tools'
    id = Column(Integer, primary_key=True, index=True)
    workorder_id = Column(Integer, ForeignKey('workorders.workorderid'))
    tool_id = Column(Integer, ForeignKey('tools_equipment.tool_id'))
    checkout_date = Column(DateTime)
    return_date = Column(DateTime)
    checked_out_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)

# --- Documentation Models ---
class TechnicalDocument(Base):
    __tablename__ = 'technical_documents'
    document_id = Column(Integer, primary_key=True, index=True)
    document_name = Column(String(100), nullable=False)
    document_type = Column(String(50))
    description = Column(Text)
    file_path = Column(String(255))
    version = Column(String(50))
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

class AssetDocument(Base):
    __tablename__ = 'asset_documents'
    id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey('assets.assetid'))
    document_id = Column(Integer, ForeignKey('technical_documents.document_id'))
    created_at = Column(DateTime, default=datetime.now)

class SafetyProcedure(Base):
    __tablename__ = 'safety_procedures'
    procedure_id = Column(Integer, primary_key=True, index=True)
    procedure_name = Column(String(100), nullable=False)
    description = Column(Text)
    procedure_steps = Column(Text)
    required_ppe = Column(Text)
    hazards = Column(Text)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now)

class WorkOrderProcedure(Base):
    __tablename__ = 'work_order_procedures'
    id = Column(Integer, primary_key=True, index=True)
    workorder_id = Column(Integer, ForeignKey('workorders.workorderid'))
    procedure_id = Column(Integer, ForeignKey('safety_procedures.procedure_id'))
    created_at = Column(DateTime, default=datetime.now)

# --- Webhook Models ---
class WebhookSubscription(Base):
    __tablename__ = 'webhook_subscriptions'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(255), ForeignKey('users.user_id'))
    endpoint_url = Column(String(255), nullable=False)
    event_types = Column(String(255), nullable=False)  # Comma-separated list of event types
    is_active = Column(Boolean, default=True)
    secret_key = Column(String(255))  # For signature verification
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationship back to User
    user = relationship("User", back_populates="webhook_subscriptions")

# --- Pydantic Models ---

class WorkOrderCreateAPI(BaseModel):
    assetId: int; workOrderType: str = Field(..., max_length=30); description: Optional[str] = None; status: str = Field(..., max_length=30); assignedTo: Optional[str] = Field(None, max_length=100); failureCode: Optional[str] = Field(None, max_length=50); failureType: Optional[str] = Field(None, max_length=50); downtimeMinutes: Optional[int] = Field(None, ge=0); repairTimeMinutes: Optional[int] = Field(None, ge=0); maintenanceCost: Optional[Decimal] = Field(None); startDate: Optional[datetime] = None; endDate: Optional[datetime] = None
    @field_validator('endDate')
    def check_end_date_after_start_date(cls, end_date, info): start_date = info.data.get('startDate'); assert not (start_date and end_date and end_date < start_date), "endDate cannot be before startDate"; return end_date
    model_config = ConfigDict(json_schema_extra={"example": {"assetId": 1, "workOrderType": "Corrective", "description": "API Test Work Order", "status": "New", "assignedTo": "API User", "failureCode": "API-F01", "failureType": "Electrical", "downtimeMinutes": 60, "repairTimeMinutes": 45, "maintenanceCost": 100.50, "startDate": "2025-04-15T10:00:00Z", "endDate": "2025-04-15T11:00:00Z"}})

class KpiHistoryPoint(BaseModel):
    timestamp: datetime; value: Optional[float]

# --- ADD Pydantic Model for Recommendation Status Update ---
class RecommendationStatusUpdate(BaseModel):
    new_status: Literal['ACKNOWLEDGED', 'DISMISSED'] # Enforce specific values

    # Optional validator (Pydantic v2 style)
    # @field_validator('new_status')

# --- ADD Pydantic Models for User Permissions ---
class UserPermissionsUpdate(BaseModel):
    permissions: List[str]
    api_access_enabled: bool = True

# --- ADD Pydantic Models for Inventory Configuration ---
class InventoryConfigCreate(BaseModel):
    parameter_name: str = Field(..., min_length=3, max_length=100)
    parameter_value: str = Field(..., min_length=1, max_length=255)
    parameter_description: Optional[str] = None
    parameter_type: str = Field(..., pattern='^(decimal|integer|string|boolean)$')

class InventoryConfigUpdate(BaseModel):
    parameter_value: str = Field(..., min_length=1, max_length=255)
    parameter_description: Optional[str] = None
    # def status_must_be_valid(cls, v):
    #     if v not in ['ACKNOWLEDGED', 'DISMISSED']:
    #         raise ValueError("Invalid status provided")
    #     return v
# --- END Pydantic Model ---

# --- Dependency to get DB session ---
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- FastAPI Lifespan for Scheduler ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    print("INFO:     Starting up scheduler...")
    scheduler.start()
    print("INFO:     Scheduler started.")
    yield
    print("INFO:     Shutting down scheduler...")
    scheduler.shutdown(wait=False)
    print("INFO:     Scheduler shut down.")

# --- FastAPI App ---
app = FastAPI(
    title="AssetKPI - Intelligent KPI & Inventory Optimization System",
    openapi_url=None,  # Disable OpenAPI schema generation to fix the 500 error
    description="""AssetKPI provides a comprehensive API for asset management, inventory optimization, and KPI calculation.

## Features

* **Asset Management**: Track and manage assets, their specifications, and performance metrics
* **Inventory Optimization**: Calculate EOQ, safety stock, and generate inventory recommendations
* **KPI Calculation**: Calculate and track maintenance KPIs like MTTR, MTBF, and failure rate
* **Work Order Management**: Create and manage maintenance work orders
* **User Management**: Manage users, roles, and permissions
* **Usage Analytics**: Track and analyze user activity and feature usage

## Authentication

The API supports two authentication methods:

* **Firebase Authentication**: For browser-based applications and user-specific operations
* **API Key Authentication**: For server-to-server communication and automated processes

## Rate Limiting

API requests are subject to rate limiting to ensure system stability:

* 100 requests per minute for authenticated users (Firebase token)
* 200 requests per minute for API key authentication

## Versioning

The current version is v0.7.1. All endpoints are prefixed with `/api`.
    """,
    version="0.7.1",
    lifespan=lifespan,
    openapi_tags=[
        {
            "name": "Authentication",
            "description": "Operations related to authentication and authorization",
        },
        {
            "name": "Assets",
            "description": "Operations for managing assets and their specifications",
        },
        {
            "name": "Inventory",
            "description": "Operations for managing inventory items and optimization",
        },
        {
            "name": "KPIs",
            "description": "Operations for calculating and retrieving KPIs",
        },
        {
            "name": "Work Orders",
            "description": "Operations for managing maintenance work orders",
        },
        {
            "name": "Users",
            "description": "Operations for managing users, roles, and permissions",
        },
        {
            "name": "Analytics",
            "description": "Operations for retrieving analytics data",
        },
        {
            "name": "Dashboard",
            "description": "Operations for dashboard data retrieval",
        },
        {
            "name": "Ingestion",
            "description": "Operations for data ingestion",
        },
    ],
    contact={
        "name": "AssetKPI Support",
        "url": "https://assetkpi.example.com/support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# Disable custom OpenAPI schema generator to fix schema generation issues
# We'll use the default FastAPI OpenAPI schema generator instead

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Add Rate Limiting middleware
app.add_middleware(
    RateLimitMiddleware,
    default_limit=100,  # 100 requests per minute for regular users
    default_window=60,  # 1 minute window
    api_key_limit=200,  # 200 requests per minute for API key users
    api_key_window=60,  # 1 minute window
    exclude_paths=["/static", "/", "/login", "/logout", "/dashboard"],  # Exclude static files and main pages
)

# Import and initialize permissions module
# We'll initialize it after defining the authentication functions

# Add Usage Tracking middleware
try:
    from middleware.usage_tracking import UsageTrackingMiddleware  # type: ignore
    app.add_middleware(UsageTrackingMiddleware, db_func=get_db)
    print("INFO:     Usage tracking middleware added successfully.")
except ImportError as e:
    print(f"WARNING:  Could not import usage tracking middleware: {e}")
    print("WARNING:  Usage tracking will not be enabled.")

# Add middleware to set rate limit headers for all responses
@app.middleware("http")
async def add_rate_limit_headers(request: Request, call_next):
    response = await call_next(request)

    # Set default rate limit headers if they don't exist
    if "X-RateLimit-Limit" not in response.headers:
        if request.headers.get("X-API-Key"):
            response.headers["X-RateLimit-Limit"] = "200"
            response.headers["X-RateLimit-Remaining"] = "200"
        else:
            response.headers["X-RateLimit-Limit"] = "100"
            response.headers["X-RateLimit-Remaining"] = "100"

    # Set reset time if it doesn't exist
    if "X-RateLimit-Reset" not in response.headers:
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + 60)

    return response

# Import test user authentication
try:
    from test_user_auth import is_test_user_token, handle_test_user_token
    print("INFO:     Test user authentication module imported successfully.")
except ImportError as e:
    print(f"WARNING:  Could not import test user authentication module: {e}")
    print("WARNING:  Test user authentication will not be available.")
    # Define dummy functions to avoid errors
    def is_test_user_token(token):
        return False
    def handle_test_user_token(token, db):
        return None

# Add middleware to store authenticated user in request state
@app.middleware("http")
async def store_user_in_request_state(request: Request, call_next):
    # Try to get the token from the Authorization header
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header.replace('Bearer ', '')

        # Check if this is a test user token
        if is_test_user_token(token):
            print(f"DEBUG: Middleware - Using test user token: {token}")
            db = SessionLocal()
            try:
                # Handle test user token
                user = handle_test_user_token(token, db)
                if user:
                    print(f"DEBUG: Middleware - Test user authenticated: {user.email}")
                    # Store user in request state
                    request.state.user = user
                else:
                    print(f"DEBUG: Middleware - Test user not found in database")
            except Exception as e:
                print(f"DEBUG: Middleware - Test user authentication failed: {e}")
            finally:
                db.close()
        else:
            # Not a test user token, proceed with Firebase authentication
            try:
                # Try to verify the token
                print(f"DEBUG: Middleware - Verifying token from Authorization header")
                decoded_token = auth.verify_id_token(token, check_revoked=True)
                firebase_uid = decoded_token.get("uid")
                if firebase_uid:
                    print(f"DEBUG: Middleware - Token verified for UID: {firebase_uid}")
                    # Get user from database
                    db = SessionLocal()
                    try:
                        user = db.query(User).filter(User.user_id == firebase_uid).first()
                        if user:
                            print(f"DEBUG: Middleware - Found user in database: {user.email}")
                            # Store user in request state
                            request.state.user = user
                        else:
                            print(f"DEBUG: Middleware - User with UID {firebase_uid} not found in database")
                    finally:
                        db.close()
                else:
                    print(f"DEBUG: Middleware - UID not found in decoded token")
            except Exception as e:
                print(f"DEBUG: Middleware - Token verification failed: {e}")
                print(f"Error verifying token in middleware: {e}")

    # Continue with the request
    return await call_next(request)

# --- Authentication Dependencies ---
from fastapi.security import OAuth2PasswordBearer

# This scheme expects a header like "Authorization: Bearer <token>"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")  # tokenUrl is placeholder, not used for validation here

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    Dependency to verify Firebase ID token or test token and retrieve user from local DB.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    if not token:
        print("ERROR: No token provided in request")
        raise credentials_exception

    # Print token info for debugging (first and last 10 chars only for security)
    token_preview = f"{token[:10]}...{token[-10:]}" if len(token) > 20 else "[token too short]"
    print(f"DEBUG: Verifying token: {token_preview}")

    # Check if this is a test user token
    if is_test_user_token(token):
        print(f"DEBUG: Using test user token in get_current_user: {token}")
        try:
            # Handle test user token
            user = handle_test_user_token(token, db)
            if user:
                print(f"DEBUG: Test user authenticated in get_current_user: {user.email}")
                # Update last login time
                user.last_login = datetime.now()
                db.commit()
                return user
            else:
                print(f"DEBUG: Test user not found in database")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Test user not found in database",
                )
        except Exception as e:
            print(f"DEBUG: Test user authentication failed in get_current_user: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Test user authentication failed: {e}",
                headers={"WWW-Authenticate": "Bearer"},
            )

    # Not a test user token, proceed with Firebase authentication
    # Print Firebase Admin SDK initialization status
    try:
        app = firebase_admin.get_app()
        print(f"DEBUG: Firebase Admin SDK initialized with app: {app.name}")
    except ValueError:
        print("ERROR: Firebase Admin SDK not initialized")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Firebase Admin SDK not initialized. Please check server logs.",
        )

    # Verify the ID token while checking if the token is revoked.
    try:
        # Check if token is in valid JWT format (should have 3 parts separated by dots)
        parts = token.split('.')
        print(f"DEBUG: Token has {len(parts)} parts (should be 3 for a valid JWT)")

        if len(parts) != 3:
            print("WARNING: Token does not have 3 parts, attempting to clean it up")
            # Try to clean up the token - sometimes line breaks or other characters get added
            token = token.strip().replace('\\s+', '')
            parts = token.split('.')
            print(f"DEBUG: After cleanup, token has {len(parts)} parts")

            if len(parts) != 3:
                print(f"ERROR: Token still invalid after cleanup: {token[:10]}...{token[-10:] if len(token) > 20 else ''}")
                raise ValueError(f"Wrong number of segments in token: {token[:20]}...")

        decoded_token = auth.verify_id_token(token, check_revoked=True)
        print(f"DEBUG: Token verified successfully")
        firebase_uid = decoded_token.get("uid")
        if firebase_uid is None:
            print("ERROR: UID not found in decoded token")
            raise credentials_exception
        print(f"DEBUG: Firebase UID from token: {firebase_uid}")
    except auth.ExpiredIdTokenError as e:
        print(f"ERROR: Firebase token expired: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token expired: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except auth.InvalidIdTokenError as e:
        print(f"ERROR: Invalid Firebase token: {e}")
        # More detailed error message for debugging
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid token: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except auth.RevokedIdTokenError as e:
        print(f"ERROR: Revoked Firebase token: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token revoked: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:  # Catch other potential firebase_admin errors
        print(f"ERROR: Firebase token verification failed: {e}")
        # More detailed error message for debugging
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token verification failed: {e}",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Token is valid, now get user from our database
    # First try with the original Firebase UID
    user = db.query(User).filter(User.user_id == firebase_uid).first()

    # If not found, try with the test admin UID for testing purposes
    if user is None and firebase_uid == "uasUzj4IXFaqJC3pcEiOCL3vD3t2":
        print(f"INFO: Trying to find test admin user with UID 'firebase-test-admin-uid' instead of {firebase_uid}")
        user = db.query(User).filter(User.user_id == "firebase-test-admin-uid").first()

    if user is None:
        # This case means the user is authenticated with Firebase,
        # but doesn't exist in our local 'users' table.
        print(f"ERROR: User with UID {firebase_uid} authenticated but not found in local DB.")

        # Get email from token for better error message
        email = decoded_token.get("email", "unknown")

        # Provide a more helpful error message with registration instructions
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "message": "User not registered in this application",
                "email": email,
                "firebase_uid": firebase_uid,
                "registration_required": True,
                "registration_url": "/api/register"
            },
        )

    # Update last login time
    user.last_login = datetime.now()
    db.commit()

    print(f"INFO: User {user.email} (Role: {user.role}) authenticated successfully.")
    return user

async def get_current_user_optional(request: Request = None, db: Session = Depends(get_db)) -> Optional[User]:
    """
    Similar to get_current_user but doesn't raise an exception if no token is provided.
    Instead, it returns None, making authentication optional.
    """
    current_user = None
    try:
        # Try to get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.replace('Bearer ', '')

            # Check if this is a test user token
            if is_test_user_token(token):
                print(f"DEBUG: Optional auth - Using test user token from header: {token}")
                try:
                    # Handle test user token
                    current_user = handle_test_user_token(token, db)
                    print(f"DEBUG: Optional auth - Test user authenticated from header: {current_user.email}")

                    # Update last login time
                    if current_user:
                        current_user.last_login = datetime.now()
                        db.commit()
                except Exception as e:
                    print(f"DEBUG: Optional auth - Test user authentication failed: {e}")
            else:
                # Try to verify the token with Firebase
                try:
                    decoded_token = auth.verify_id_token(token, check_revoked=True)
                    firebase_uid = decoded_token.get("uid")
                    if firebase_uid:
                        # Get user from database
                        current_user = db.query(User).filter(User.user_id == firebase_uid).first()

                        # If not found, try with the test admin UID for testing purposes
                        if current_user is None and firebase_uid == "uasUzj4IXFaqJC3pcEiOCL3vD3t2":
                            print(f"INFO: Trying to find test admin user with UID 'firebase-test-admin-uid' instead of {firebase_uid}")
                            current_user = db.query(User).filter(User.user_id == "firebase-test-admin-uid").first()

                        # Update last login time if user exists
                        if current_user:
                            current_user.last_login = datetime.now()
                            db.commit()
                except Exception as e:
                    print(f"DEBUG: Optional auth - Token verification failed: {e}")

        else:
            # Try to get token from cookie
            try:
                token = request.cookies.get('firebaseIdToken')
                if token:
                    # Check if this is a test user token
                    if is_test_user_token(token):
                        print(f"DEBUG: Optional auth - Using test user token from cookie: {token}")
                        try:
                            # Handle test user token
                            current_user = handle_test_user_token(token, db)
                            print(f"DEBUG: Optional auth - Test user authenticated from cookie: {current_user.email}")

                            # Update last login time
                            if current_user:
                                current_user.last_login = datetime.now()
                                db.commit()
                        except Exception as e:
                            print(f"DEBUG: Optional auth - Test user authentication failed: {e}")
                    else:
                        # Similar verification logic as above
                        try:
                            decoded_token = auth.verify_id_token(token, check_revoked=True)
                            firebase_uid = decoded_token.get("uid")
                            if firebase_uid:
                                current_user = db.query(User).filter(User.user_id == firebase_uid).first()

                                # If not found, try with the test admin UID for testing purposes
                                if current_user is None and firebase_uid == "uasUzj4IXFaqJC3pcEiOCL3vD3t2":
                                    print(f"INFO: Trying to find test admin user with UID 'firebase-test-admin-uid' instead of {firebase_uid}")
                                    current_user = db.query(User).filter(User.user_id == "firebase-test-admin-uid").first()

                                # Update last login time if user exists
                                if current_user:
                                    current_user.last_login = datetime.now()
                                    db.commit()
                        except Exception as e:
                            print(f"DEBUG: Optional auth - Cookie token verification failed: {e}")
            except Exception as e:
                print(f"DEBUG: Optional auth - Cookie access failed: {e}")
    except Exception as e:
        print(f"DEBUG: Optional auth - Authentication attempt failed: {e}")

    return current_user

def require_role(required_roles: list[UserRole]):
    """
    Factory for creating dependency that checks if user has one of the required roles.
    """
    async def role_checker(current_user: User = Depends(get_current_user)):
        if current_user.role not in required_roles:
            print(f"ERROR: User {current_user.email} (Role: {current_user.role}) does not have required role(s): {required_roles}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required role: {required_roles}",
            )
        return current_user
    return role_checker

# Define common role requirements
require_admin = require_role([UserRole.ADMIN])
require_manager_or_admin = require_role([UserRole.MANAGER, UserRole.ADMIN])
require_manager_plus = require_role([UserRole.MANAGER, UserRole.ADMIN])  # Alias for manager_or_admin
require_engineer_plus = require_role([UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN])
require_viewer_plus = require_role([UserRole.VIEWER, UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN])

# Initialize permissions module now that authentication functions are defined
try:
    # Add the current directory to the Python path
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # First try to import from the permissions package
    try:
        from permissions import PermissionMiddleware, init_permissions  # type: ignore
        print("INFO:     Successfully imported permissions module from package")
    except ImportError:
        # Fall back to direct import
        from permissions_module import PermissionMiddleware, init_permissions  # type: ignore
        print("INFO:     Successfully imported permissions module from file")

    # Initialize permissions module with dependencies
    init_permissions(
        user_model=User,
        user_permission_model=UserPermission,
        session_local=SessionLocal,
        get_db_func=get_db,
        get_current_user_func=get_current_user
    )
    # Add permission middleware
    app.add_middleware(PermissionMiddleware)
    print("INFO:     Permission middleware added successfully.")
except ImportError as e:
    print(f"WARNING:  Could not import permissions module: {e}")
    print("WARNING:  Permission checking will not be enabled.")

# --- Helper Functions ---

def save_calculated_kpi(db: Session, name: str, value: Optional[float | Decimal | int], unit: str, source: str = "scheduled_job", asset_id: Optional[int] = None):
    if value is None: print(f"Skipping save for KPI '{name}' due to null value."); return
    try:
        numeric_value = float(value) if isinstance(value, Decimal) else value
        history_entry = CalculatedKpiHistory(kpi_name=name, kpi_value=numeric_value, kpi_unit=unit, asset_id=asset_id, calculation_source=source); db.add(history_entry); db.flush()
        print(f"Prepared KPI for save: {name} = {value} {unit} (Source: {source})")
    except Exception as e: print(f"ERROR preparing KPI '{name}' for history save: {e}")

def generate_or_update_recommendation(db: Session, part_id: int, rec_type: str, reason: str, priority: int = 3):
    try:
        existing_rec = db.query(InventoryRecommendation).filter(InventoryRecommendation.part_id == part_id, InventoryRecommendation.recommendation_type == rec_type, InventoryRecommendation.status == 'NEW').with_for_update().first()
        if existing_rec:
            if existing_rec.reason != reason or existing_rec.priority != priority:
                print(f"Updating existing NEW recommendation for part {part_id}, type {rec_type}"); existing_rec.reason = reason; existing_rec.priority = priority
            else: print(f"Recommendation already exists and is unchanged for part {part_id}, type {rec_type}")
        else: print(f"Creating NEW recommendation for part {part_id}, type {rec_type}"); new_rec = InventoryRecommendation(part_id=part_id, recommendation_type=rec_type, reason=reason, priority=priority, status='NEW'); db.add(new_rec)
        db.flush()
    except Exception as e: print(f"ERROR generating/updating recommendation for part {part_id}, type {rec_type}: {e}")

# --- Enhanced EOQ Calculation Function ---
def calculate_eoq(
    annual_demand: Optional[float],
    ordering_cost: Decimal,
    unit_cost: Optional[Decimal],
    holding_cost_percent: Decimal
) -> Optional[float]:
    """Calculates Economic Order Quantity."""
    if annual_demand is None or annual_demand <= 0 or \
       unit_cost is None or unit_cost <= 0 or \
       ordering_cost <= 0 or holding_cost_percent <= 0:
        return None # Cannot calculate if inputs are invalid/zero

    try:
        # Convert Decimal to float for calculations
        unit_cost_float = float(unit_cost) if unit_cost is not None else 0
        ordering_cost_float = float(ordering_cost)
        holding_cost_percent_float = float(holding_cost_percent)

        annual_holding_cost_per_unit = unit_cost_float * holding_cost_percent_float
        if annual_holding_cost_per_unit == 0: return None # Avoid division by zero

        # EOQ formula: sqrt((2 * Demand * OrderCost) / HoldingCost)
        # Ensure inputs are floats for math.sqrt
        eoq_value = math.sqrt((2 * float(annual_demand) * ordering_cost_float) / annual_holding_cost_per_unit)
        return round(eoq_value, 0) # Often rounded to whole units
    except Exception as e:
        print(f"Error calculating EOQ: {e}")
        return None

def calculate_and_store_eoq(
    db: Session,
    part_id: int,
    annual_demand: Optional[float],
    ordering_cost: Decimal,
    unit_cost: Optional[Decimal],
    holding_cost_percent: Decimal
) -> Optional[float]:
    """Calculates EOQ and stores the results in the database."""
    try:
        # Calculate EOQ
        eoq_value = calculate_eoq(annual_demand, ordering_cost, unit_cost, holding_cost_percent)
        if eoq_value is None:
            return None

        # Calculate additional metrics
        # Convert Decimal to float for calculations
        unit_cost_float = float(unit_cost) if unit_cost is not None else 0
        ordering_cost_float = float(ordering_cost)
        holding_cost_percent_float = float(holding_cost_percent)

        annual_holding_cost_per_unit = unit_cost_float * holding_cost_percent_float
        optimal_order_frequency = annual_demand / eoq_value if eoq_value > 0 else 0
        annual_ordering_cost = ordering_cost_float * optimal_order_frequency
        annual_holding_cost = annual_holding_cost_per_unit * (eoq_value / 2)  # Average inventory = EOQ/2
        total_annual_cost = annual_ordering_cost + annual_holding_cost

        # Store the calculation in the database
        eoq_calc = EOQCalculation(
            part_id=part_id,
            annual_demand=Decimal(str(annual_demand)) if annual_demand is not None else None,
            ordering_cost=ordering_cost,
            holding_cost=annual_holding_cost_per_unit,
            eoq_value=Decimal(str(eoq_value)),
            annual_ordering_cost=Decimal(str(annual_ordering_cost)),
            annual_holding_cost=Decimal(str(annual_holding_cost)),
            total_annual_cost=Decimal(str(total_annual_cost)),
            optimal_order_frequency=int(optimal_order_frequency) if optimal_order_frequency > 0 else 0,
            calculated_at=datetime.now()
        )
        db.add(eoq_calc)
        db.flush()

        # Update the part with the EOQ value
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if part:
            part.eoq = Decimal(str(eoq_value))
            part.annual_demand = Decimal(str(annual_demand)) if annual_demand is not None else None
            part.ordering_cost = ordering_cost
            part.holding_cost_percent = holding_cost_percent

        return eoq_value
    except Exception as e:
        print(f"Error calculating and storing EOQ: {e}")
        return None
# --- END Enhanced EOQ Function ---

# --- Enhanced Safety Stock Calculation Function ---
def calculate_safety_stock_simple(
    avg_daily_demand: Optional[float],
    lead_time_days: Optional[int],
    demand_variability_factor: Decimal, # Placeholder representing std dev / avg demand
    lead_time_variability_factor: Decimal, # Placeholder representing std dev / avg lead time
    service_level_z: Decimal = SAFETY_STOCK_SERVICE_LEVEL_Z # Z-score for desired service level
) -> Optional[float]:
    """Calculates Safety Stock using a simplified variability factor approach."""
    # This is a highly simplified method. Robust calculation needs actual std deviations.
    if avg_daily_demand is None or avg_daily_demand < 0 or \
       lead_time_days is None or lead_time_days <= 0:
        return None

    try:
        # Estimate standard deviations based on factors (VERY rough)
        std_dev_demand = avg_daily_demand * float(demand_variability_factor)
        std_dev_lead_time = lead_time_days * float(lead_time_variability_factor)

        # Common simplified formula (assumes normal distribution, independent vars)
        # Safety Stock = Z * sqrt((Avg Lead Time * StdDev Demand^2) + (Avg Daily Demand * StdDev Lead Time)^2)
        variance_demand_lead = (lead_time_days * (std_dev_demand**2))
        variance_lead_demand = ((avg_daily_demand**2) * (std_dev_lead_time**2))

        # Check for potential negative values before sqrt if inputs were odd
        if variance_demand_lead < 0 or variance_lead_demand < 0:
             print("Warning: Negative variance component in safety stock calc.")
             return None

        combined_std_dev = math.sqrt(variance_demand_lead + variance_lead_demand)
        safety_stock_value = float(service_level_z) * combined_std_dev

        return round(safety_stock_value, 0) # Round to whole units
    except Exception as e:
        print(f"Error calculating Safety Stock: {e}")
        return None

def calculate_and_store_safety_stock(
    db: Session,
    part_id: int,
    avg_daily_demand: Optional[float],
    lead_time_days: Optional[int],
    demand_variability_factor: Decimal,
    lead_time_variability_factor: Decimal,
    service_level_z: Decimal = SAFETY_STOCK_SERVICE_LEVEL_Z
) -> Optional[float]:
    """Calculates safety stock and stores the results in the database."""
    try:
        # Calculate safety stock
        safety_stock = calculate_safety_stock_simple(
            avg_daily_demand,
            lead_time_days,
            demand_variability_factor,
            lead_time_variability_factor,
            service_level_z
        )
        if safety_stock is None:
            return None

        # Calculate reorder point
        reorder_point = (avg_daily_demand * lead_time_days) + safety_stock

        # Store the calculation in the database
        ss_calc = SafetyStockCalculation(
            part_id=part_id,
            avg_daily_demand=Decimal(str(avg_daily_demand)) if avg_daily_demand is not None else None,
            lead_time_days=lead_time_days,
            demand_variability=demand_variability_factor,
            lead_time_variability=lead_time_variability_factor,
            service_level=service_level_z,
            service_level_z=service_level_z,
            safety_stock_value=Decimal(str(safety_stock)),
            reorder_point=Decimal(str(reorder_point)),
            calculated_at=datetime.now()
        )
        db.add(ss_calc)
        db.flush()

        # Update the part with the safety stock value
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if part:
            part.calculated_safety_stock = Decimal(str(safety_stock))
            part.reorder_point = Decimal(str(reorder_point))
            part.demand_variability = demand_variability_factor
            part.lead_time_variability = lead_time_variability_factor
            part.service_level = service_level_z

        return safety_stock
    except Exception as e:
        print(f"Error calculating and storing safety stock: {e}")
        return None
# --- END Enhanced Safety Stock Function ---

def create_work_order_object(wo_data: dict) -> WorkOrder:
    asset_id = wo_data.get('assetId'); assert asset_id is not None, "assetId is required"
    start_date = wo_data.get('startDate'); end_date = wo_data.get('endDate')
    assert not (start_date and end_date and isinstance(start_date, datetime) and isinstance(end_date, datetime) and end_date < start_date), "endDate cannot be before startDate"
    wo = WorkOrder(
        assetid=int(asset_id), workordertype=str(wo_data.get('workOrderType','')), description=str(wo_data.get('description','')) if wo_data.get('description') else None, status=str(wo_data.get('status','')),
        assignedto=str(wo_data.get('assignedTo','')) if wo_data.get('assignedTo') else None, failurecode=str(wo_data.get('failureCode','')) if wo_data.get('failureCode') else None, failuretype=str(wo_data.get('failureType','')) if wo_data.get('failureType') else None,
        downtimeminutes=int(wo_data['downtimeMinutes']) if wo_data.get('downtimeMinutes') is not None else None, repairtimeminutes=int(wo_data['repairTimeMinutes']) if wo_data.get('repairTimeMinutes') is not None else None,
        maintenancecost=Decimal(str(wo_data['maintenanceCost'])) if wo_data.get('maintenanceCost') is not None else None, startdate=start_date, enddate=end_date
    ); return wo

# --- Data Retrieval & Calculation Functions ---

def get_latest_kpis(db: Session) -> Optional[KpiReport]:
    try: return db.query(KpiReport).order_by(desc(KpiReport.reportdate)).first()
    except Exception as e: print(f"Error fetching latest KPIs: {e}"); return None

def calculate_mttr_from_workorders(db: Session) -> Optional[float]:
    try:
        relevant_workorders = db.query(WorkOrder.repairtimeminutes).filter(WorkOrder.workordertype == 'Corrective', WorkOrder.repairtimeminutes.isnot(None), WorkOrder.repairtimeminutes > 0).all()
        if not relevant_workorders: return 0.0
        repair_times = [r[0] for r in relevant_workorders]; total_repair_minutes = sum(repair_times); number_of_repairs = len(repair_times)
        if number_of_repairs > 0: mttr_minutes = total_repair_minutes / number_of_repairs; mttr_hours = mttr_minutes / 60.0; return round(mttr_hours, 2)
        else: return 0.0
    except Exception as e: print(f"Error during MTTR calculation: {e}"); return None

def get_failure_and_operating_time(db: Session) -> Tuple[int, timedelta]:
    number_of_failures = 0; total_operating_time = timedelta(0)
    try:
        corrective_workorders = db.query(WorkOrder.workorderid, WorkOrder.assetid, WorkOrder.startdate, WorkOrder.enddate).filter(WorkOrder.workordertype == 'Corrective', WorkOrder.startdate.isnot(None), WorkOrder.enddate.isnot(None)).order_by(WorkOrder.assetid, WorkOrder.startdate).all()
        if not corrective_workorders: return 0, timedelta(0)
        last_wo_end_date: Optional[datetime] = None; current_asset_id: Optional[int] = None
        for wo_id, assetid, startdate, enddate in corrective_workorders:
            if assetid != current_asset_id: current_asset_id = assetid; last_wo_end_date = enddate; number_of_failures += 1; continue
            if last_wo_end_date and startdate:
                time_between = startdate - last_wo_end_date
                if time_between > timedelta(0): total_operating_time += time_between
                else: print(f"MTBF/FR Calc Warning: WO {wo_id} start date ({startdate}) <= previous WO end date ({last_wo_end_date}) for asset {assetid}. Skipping.")
                number_of_failures += 1; last_wo_end_date = enddate
            else: print(f"MTBF/FR Calc Warning: Missing dates for WO {wo_id} or previous. Skipping."); last_wo_end_date = enddate
        return number_of_failures, total_operating_time
    except Exception as e: print(f"Error calculating Failure/Operating Time: {e}"); return 0, timedelta(0)

def calculate_mtbf(number_of_failures: int, total_operating_time: timedelta) -> Optional[float]:
    if number_of_failures == 0: print("MTBF Calc: Zero failures."); return None
    if total_operating_time <= timedelta(0): print("MTBF Calc: Non-positive operating time."); return 0.0
    try: mtbf_hours = (total_operating_time.total_seconds() / 3600) / number_of_failures; return round(mtbf_hours, 2)
    except Exception as e: print(f"Error in MTBF division: {e}"); return None

def calculate_failure_rate(number_of_failures: int, total_operating_time: timedelta) -> Optional[float]:
    if number_of_failures == 0: return 0.0
    if total_operating_time <= timedelta(0): print("FR Calc: Non-positive operating time."); return None
    try:
        total_operating_years = total_operating_time.total_seconds() / (3600 * 24 * 365.25)
        if total_operating_years == 0: print("FR Calc: Operating time too small for yearly rate."); return None
        failure_rate_per_year = number_of_failures / total_operating_years; return round(failure_rate_per_year, 4)
    except Exception as e: print(f"Error in Failure Rate calculation: {e}"); return None

def calculate_workorder_data_quality(db: Session) -> Dict[str, Any]:
    results = {'total_wos': 0, 'missing_repairtime': 0, 'missing_startdate': 0, 'missing_enddate': 0, 'corrective_wos': 0, 'invalid_corrective_intervals': 0}
    try:
        all_wos = db.query(WorkOrder.workorderid, WorkOrder.assetid, WorkOrder.workordertype, WorkOrder.repairtimeminutes, WorkOrder.startdate, WorkOrder.enddate).order_by(WorkOrder.assetid, WorkOrder.startdate).all()
        results['total_wos'] = len(all_wos);
        if results['total_wos'] == 0: return results
        last_corrective_wo_end_date: Optional[datetime] = None; current_asset_id: Optional[int] = None
        for wo_id, assetid, wotype, repairtime, startdate, enddate in all_wos:
            if repairtime is None or repairtime <= 0: results['missing_repairtime'] += 1
            if startdate is None: results['missing_startdate'] += 1
            if enddate is None: results['missing_enddate'] += 1
            if wotype == 'Corrective':
                results['corrective_wos'] += 1;
                if startdate is None or enddate is None: continue
                if assetid != current_asset_id: current_asset_id = assetid; last_corrective_wo_end_date = enddate
                else:
                    if last_corrective_wo_end_date and startdate <= last_corrective_wo_end_date: results['invalid_corrective_intervals'] += 1
                    last_corrective_wo_end_date = enddate
        return results
    except Exception as e: print(f"Error calculating work order data quality: {e}"); return {}

# --- Scheduled Job Functions ---
def run_kpi_calculations_job():
    print(f"--- Running Scheduled KPI Calculation Job at {datetime.now()} ---")
    db: Session = SessionLocal()
    source_tag = "scheduled_job"
    try:
        mttr_value = calculate_mttr_from_workorders(db)
        save_calculated_kpi(db, name="MTTR_Calculated", value=mttr_value, unit="hours", source=source_tag)

        num_failures, op_time = get_failure_and_operating_time(db)
        mtbf_value = calculate_mtbf(num_failures, op_time)
        save_calculated_kpi(db, name="MTBF_Calculated", value=mtbf_value, unit="hours", source=source_tag)

        fr_value = calculate_failure_rate(num_failures, op_time)
        save_calculated_kpi(db, name="FailureRate_Calculated", value=fr_value, unit="failures/year", source=source_tag)

        quality_metrics = calculate_workorder_data_quality(db)
        save_calculated_kpi(db, name="DQ_InvalidIntervals", value=quality_metrics.get('invalid_corrective_intervals'), unit="count", source=source_tag)
        save_calculated_kpi(db, name="DQ_MissingRepairTime", value=quality_metrics.get('missing_repairtime'), unit="count", source=source_tag)

        db.commit()
        print(f"--- Finished Scheduled KPI Job. Changes committed. ---")
    except Exception as e:
        print(f"!!! ERROR in Scheduled KPI Job: {e} !!!")
        db.rollback()
    finally:
        db.close()

def analyze_inventory_and_store_results(db: Session, part_id: int) -> Optional[InventoryAnalysis]:
    """Analyzes inventory for a specific part and stores the results."""
    try:
        # Get the part
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if not part:
            print(f"Part with ID {part_id} not found")
            return None

        # Get the latest EOQ and safety stock calculations
        eoq_calc = db.query(EOQCalculation).filter(EOQCalculation.part_id == part_id).order_by(desc(EOQCalculation.calculated_at)).first()
        ss_calc = db.query(SafetyStockCalculation).filter(SafetyStockCalculation.part_id == part_id).order_by(desc(SafetyStockCalculation.calculated_at)).first()

        if not eoq_calc or not ss_calc:
            print(f"Missing EOQ or safety stock calculations for part {part_id}")
            return None

        # Calculate optimal stock level
        # A simple approach: EOQ/2 (average inventory) + safety stock
        optimal_stock = int((float(eoq_calc.eoq_value) / 2) + float(ss_calc.safety_stock_value))

        # Calculate current stock level
        current_stock = part.stockquantity or 0

        # Calculate stock difference
        stock_difference = current_stock - optimal_stock

        # Calculate costs
        unit_price = float(part.unitprice) if part.unitprice else 0
        current_cost = current_stock * unit_price
        optimal_cost = optimal_stock * unit_price
        potential_savings = current_cost - optimal_cost if current_cost > optimal_cost else 0

        # Calculate days of supply
        avg_daily_demand = float(ss_calc.avg_daily_demand) if ss_calc.avg_daily_demand else 0
        days_of_supply = int(current_stock / avg_daily_demand) if avg_daily_demand > 0 else 0

        # Calculate stockout risk
        # Simple approach: if current stock < reorder point, risk is high
        stockout_risk = 0.0
        if ss_calc.reorder_point and current_stock < float(ss_calc.reorder_point):
            # Calculate as percentage below reorder point
            deficit = float(ss_calc.reorder_point) - current_stock
            stockout_risk = min(100.0, (deficit / float(ss_calc.reorder_point)) * 100)

        # Store the analysis
        analysis = InventoryAnalysis(
            part_id=part_id,
            current_stock=current_stock,
            optimal_stock=optimal_stock,
            stock_difference=stock_difference,
            current_cost=Decimal(str(current_cost)),
            optimal_cost=Decimal(str(optimal_cost)),
            potential_savings=Decimal(str(potential_savings)),
            days_of_supply=days_of_supply,
            stockout_risk=Decimal(str(stockout_risk)),
            last_usage_date=part.last_usage_date,
            analysis_date=datetime.now()
        )
        db.add(analysis)
        db.flush()

        # Update the part with days of supply
        part.days_of_supply = days_of_supply

        return analysis
    except Exception as e:
        print(f"Error analyzing inventory for part {part_id}: {e}")
        return None

def run_inventory_optimization_job():
    print(f"--- Running Scheduled Inventory Optimization Job at {datetime.now()} ---")
    db: Session = SessionLocal()
    try:
        parts_to_check = db.query(Sparepart).all()
        if not parts_to_check:
            print("Inventory Job: No spare parts found.")
            return

        # Get configuration values from database or use defaults
        overstock_days = int(get_config_value(db, 'overstock_days_threshold', 180))
        obsolete_days = int(get_config_value(db, 'obsolete_days_threshold', 365))
        ss_multiplier = float(get_config_value(db, 'overstock_safety_stock_multiplier', 3))
        eoq_threshold = float(get_config_value(db, 'eoq_order_threshold', 0.8))

        print(f"Using configuration values: overstock_days={overstock_days}, obsolete_days={obsolete_days}, ss_multiplier={ss_multiplier}, eoq_threshold={eoq_threshold}")

        today = datetime.now().date()
        for part in parts_to_check:
            # Run inventory analysis
            analysis = analyze_inventory_and_store_results(db, part.partid)

            # Check for restock alerts based on reorder point
            if part.stockquantity is not None and part.reorder_point is not None and part.stockquantity <= float(part.reorder_point):
                reason = f"Stock ({part.stockquantity}) <= Reorder Point ({float(part.reorder_point):.0f})."
                generate_or_update_recommendation(db, part.partid, 'RESTOCK_ALERT', reason, 1)

            # Check for EOQ-based order recommendations
            if part.eoq is not None and part.stockquantity is not None:
                # If stock is below EOQ threshold, recommend ordering up to EOQ
                if part.stockquantity < (float(part.eoq) * eoq_threshold):
                    order_qty = int(float(part.eoq)) - part.stockquantity
                    reason = f"Stock ({part.stockquantity}) below EOQ threshold. Recommend ordering {order_qty} units to reach optimal EOQ ({int(float(part.eoq))})."
                    generate_or_update_recommendation(db, part.partid, 'EOQ_ORDER', reason, 2)

            # Check for overstock alerts
            if part.stockquantity is not None and part.calculated_safety_stock is not None and part.lastrestocked is not None and isinstance(part.lastrestocked, date):
                os_level = float(part.calculated_safety_stock) * ss_multiplier
                last_restocked_date = part.lastrestocked
                if part.stockquantity > os_level and (today - last_restocked_date).days > overstock_days:
                    reason = f"Stock ({part.stockquantity}) > Calculated Level ({os_level:.0f}) & Last Restock > {overstock_days} days ago ({last_restocked_date})."
                    generate_or_update_recommendation(db, part.partid, 'OVERSTOCK_ALERT', reason, 3)

            # Check for obsolete parts
            if part.stockquantity is not None and part.stockquantity > 0 and part.lastrestocked is not None and isinstance(part.lastrestocked, date):
                last_restocked_date = part.lastrestocked
                if (today - last_restocked_date).days > obsolete_days:
                    reason = f"Stock ({part.stockquantity}) > 0 & Last Restock > {obsolete_days} days ago ({last_restocked_date})."
                    generate_or_update_recommendation(db, part.partid, 'OBSOLETE_FLAG', reason, 3)

        db.commit()
        print(f"--- Finished Scheduled Inventory Optimization Job. Changes committed. ---")
    except Exception as e:
        print(f"!!! ERROR in Scheduled Inventory Optimization Job: {e} !!!")
        db.rollback()
    finally:
        db.close()

# --- Define the update_inventory_metrics_job function ---
def update_inventory_metrics_job():
    """
    Scheduled job to calculate consumption, ABC, EOQ, Safety Stock
    and update the spareparts table.
    """
    print(f"--- Running Scheduled Inventory Metrics Job at {datetime.now()} ---")
    db: Session = SessionLocal()
    try:
        # --- 1. Calculate Consumption Rate (as before) ---
        print("Calculating average monthly consumption...")
        analysis_end_date = datetime.now(); analysis_start_date = analysis_end_date - timedelta(days=365)
        analysis_months = 365.0 / 30.4
        consumption_query = db.query(
                WorkOrderParts.partid, func.sum(WorkOrderParts.quantityused).label('total_consumed')
            ).join(WorkOrder, WorkOrderParts.workorderid == WorkOrder.workorderid)\
            .filter(WorkOrder.enddate >= analysis_start_date, WorkOrder.enddate < analysis_end_date)\
            .group_by(WorkOrderParts.partid)
        consumption_data = {part_id: total for part_id, total in consumption_query.all()}
        print(f"Found consumption data for {len(consumption_data)} parts.")

        # --- 2. Update Metrics & Calculate Annual Value/Demand ---
        print("Updating spareparts table with consumption, EOQ, Safety Stock...")
        parts_for_abc = []; all_parts = db.query(Sparepart).all()
        updated_count = 0; current_update_time = datetime.now(analysis_end_date.tzinfo)

        for part in all_parts:
            total_consumed = consumption_data.get(part.partid, 0)
            avg_monthly_cons = (total_consumed / analysis_months) if analysis_months > 0 else 0.0
            avg_daily_cons = avg_monthly_cons / 30.4 if avg_monthly_cons > 0 else 0.0
            annual_demand = avg_monthly_cons * 12

            # Update consumption
            part.avg_monthly_consumption = round(Decimal(str(avg_monthly_cons)), 2)
            part.inventory_metrics_last_updated = current_update_time
            updated_count += 1

            # --- Enhanced EOQ Calculation ---
            # Get configuration values from database or use defaults
            ordering_cost = get_config_value(db, 'ordering_cost', DEFAULT_ORDERING_COST)
            holding_cost_percent = get_config_value(db, 'holding_cost_percent', DEFAULT_ANNUAL_HOLDING_COST_PERCENT)

            # Calculate and store EOQ
            eoq_result = calculate_and_store_eoq(
                db=db,
                part_id=part.partid,
                annual_demand=annual_demand,
                ordering_cost=ordering_cost,
                unit_cost=part.unitprice,
                holding_cost_percent=holding_cost_percent
            )
            # --- END Enhanced EOQ ---

            # --- Enhanced Safety Stock Calculation ---
            # Get configuration values from database or use defaults
            service_level_z = get_config_value(db, 'service_level_z', SAFETY_STOCK_SERVICE_LEVEL_Z)
            demand_variability_factor = get_config_value(db, 'demand_variability_factor', SAFETY_STOCK_DEMAND_VARIABILITY_FACTOR)
            lead_time_variability_factor = get_config_value(db, 'lead_time_variability_factor', SAFETY_STOCK_LEAD_TIME_VARIABILITY_FACTOR)

            # Calculate and store safety stock
            ss_result = calculate_and_store_safety_stock(
                db=db,
                part_id=part.partid,
                avg_daily_demand=avg_daily_cons,
                lead_time_days=part.leadtimedays,
                demand_variability_factor=demand_variability_factor,
                lead_time_variability_factor=lead_time_variability_factor,
                service_level_z=service_level_z
            )
            # --- END Enhanced Safety Stock ---

            # --- Run Inventory Analysis ---
            analyze_inventory_and_store_results(db, part.partid)
            # --- END Inventory Analysis ---


            # Prepare for ABC (as before)
            if part.unitprice is not None and part.unitprice > 0:
                annual_value = annual_demand * float(part.unitprice)
                parts_for_abc.append({'partid': part.partid, 'annual_value': annual_value})
            else:
                parts_for_abc.append({'partid': part.partid, 'annual_value': 0.0})

        print(f"Prepared/Updated {updated_count} parts with consumption/EOQ/SS.")

        # --- 3. Calculate and Update ABC Classification (as before) ---
        print("Calculating ABC Classification...")
        if parts_for_abc:
            parts_for_abc.sort(key=lambda x: x['annual_value'], reverse=True)
            total_system_value = sum(p['annual_value'] for p in parts_for_abc)
            print(f"Total annual consumption value: {total_system_value:.2f}")
            cumulative_value = 0.0; class_a_threshold = 0.80; class_b_threshold = 0.95
            abc_updates = {}
            if total_system_value > 0:
                for part_data in parts_for_abc:
                    cumulative_value += part_data['annual_value']
                    cumulative_perc = cumulative_value / total_system_value
                    part_id = part_data['partid']
                    if cumulative_perc <= class_a_threshold: abc_updates[part_id] = 'A'
                    elif cumulative_perc <= class_b_threshold: abc_updates[part_id] = 'B'
                    else: abc_updates[part_id] = 'C'
            else:
                 for part_data in parts_for_abc: abc_updates[part_data['partid']] = 'C'

            abc_updated_count = 0
            for part in all_parts: # Loop through already fetched parts
                new_class = abc_updates.get(part.partid)
                if new_class and part.abc_classification != new_class:
                    part.abc_classification = new_class; abc_updated_count +=1
                elif not new_class and part.abc_classification is not None:
                     part.abc_classification = None # Or 'C'
            print(f"Assigned/Updated ABC classification for {len(abc_updates)} parts. Changed: {abc_updated_count}")
        else:
             print("No parts data available for ABC classification.")

        # --- Commit all updates ---
        db.commit()
        print(f"--- Finished Scheduled Inventory Metrics Job. Changes committed. ---")

    except Exception as e:
        print(f"!!! ERROR in Scheduled Inventory Metrics Job: {e} !!!")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()

# --- Schedule the Jobs ---
scheduler.add_job(run_kpi_calculations_job, 'interval', minutes=1, id='kpi_calc_job')
scheduler.add_job(run_inventory_optimization_job, 'interval', minutes=1, id='inv_opt_job')
scheduler.add_job(update_inventory_metrics_job, 'interval', minutes=1, id='inv_metrics_job')

# --- API Endpoints ---

# Auth dependencies are defined above

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request, db: Session = Depends(get_db)):
    # Make authentication optional for the dashboard page
    current_user = None
    try:
        print("DEBUG: Starting dashboard rendering")
        # Try to get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.replace('Bearer ', '')
            # Print token info for debugging (first and last 10 chars only for security)
            token_preview = f"{token[:10]}...{token[-10:]}" if len(token) > 20 else "[token too short]"
            print(f"DEBUG: Dashboard - Found token in header: {token_preview}")

            # Try to verify the token
            try:
                decoded_token = auth.verify_id_token(token, check_revoked=True)
                firebase_uid = decoded_token.get("uid")
                if firebase_uid:
                    # Get user from database
                    current_user = db.query(User).filter(User.user_id == firebase_uid).first()
                    print(f"DEBUG: Dashboard - Authenticated user: {current_user.email if current_user else 'Not found in DB'}")
            except Exception as e:
                print(f"DEBUG: Dashboard - Token verification failed: {e}")
        else:
            # Try to get token from cookie
            try:
                token = request.cookies.get('firebaseIdToken')
                if token:
                    print(f"DEBUG: Dashboard - Found token in cookie")

                    # Check if this is a test user token
                    if is_test_user_token(token):
                        print(f"DEBUG: Dashboard - Using test user token from cookie: {token}")
                        try:
                            # Handle test user token
                            current_user = handle_test_user_token(token, db)
                            print(f"DEBUG: Dashboard - Test user authenticated from cookie: {current_user.email}")
                        except Exception as e:
                            print(f"DEBUG: Dashboard - Test user authentication failed: {e}")
                    else:
                        # Similar verification logic as above
                        try:
                            decoded_token = auth.verify_id_token(token, check_revoked=True)
                            firebase_uid = decoded_token.get("uid")
                            if firebase_uid:
                                current_user = db.query(User).filter(User.user_id == firebase_uid).first()
                                print(f"DEBUG: Dashboard - Authenticated user from cookie: {current_user.email if current_user else 'Not found in DB'}")
                        except Exception as e:
                            print(f"DEBUG: Dashboard - Cookie token verification failed: {e}")
            except Exception as e:
                print(f"DEBUG: Dashboard - Error getting cookie: {e}")
    except Exception as e:
        print(f"DEBUG: Dashboard - Authentication attempt failed: {e}")
        import traceback
        traceback.print_exc()

    try:
        print("DEBUG: Fetching dashboard data")
        # Continue with normal dashboard rendering
        latest_kpis_from_report = get_latest_kpis(db)
        print("DEBUG: Got latest KPIs from report")
        workorder_quality = calculate_workorder_data_quality(db)
        print("DEBUG: Got workorder quality data")
        latest_stored_results = {}
        kpis_to_fetch = ['MTTR_Calculated', 'MTBF_Calculated', 'FailureRate_Calculated', 'DQ_InvalidIntervals', 'DQ_MissingRepairTime']
        try:
            for kpi_name in kpis_to_fetch:
                latest_stored_results[kpi_name] = db.query(CalculatedKpiHistory).filter(CalculatedKpiHistory.kpi_name == kpi_name).order_by(desc(CalculatedKpiHistory.calculation_timestamp)).first()
            print("DEBUG: Got latest stored KPIs")
        except Exception as e:
            print(f"Error fetching latest stored KPIs: {e}")
            import traceback
            traceback.print_exc()
        active_recommendations = []
    except Exception as e:
        print(f"DEBUG: Error fetching dashboard data: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard data: {str(e)}")
    try:
        print("DEBUG: Fetching recommendations")
        active_recommendations = db.query(InventoryRecommendation, Sparepart.partid, Sparepart.partname, Sparepart.partnumber).join(Sparepart, InventoryRecommendation.part_id == Sparepart.partid).filter(InventoryRecommendation.status == 'NEW').order_by(asc(InventoryRecommendation.priority), desc(InventoryRecommendation.generated_at)).all()
        print("DEBUG: Got recommendations")
    except Exception as e:
        print(f"Error fetching inventory recommendations: {e}")
        import traceback
        traceback.print_exc()
        active_recommendations = []

    all_spare_parts = []
    try:
        print("DEBUG: Fetching spare parts")
        # Fetch all parts, order ONLY by name for simplicity to avoid syntax error
        all_spare_parts = db.query(Sparepart).order_by(asc(Sparepart.partname)).all()
        print(f"DEBUG: Fetched {len(all_spare_parts)} spare parts for dashboard (sorted by name)")
    except Exception as e:
        print(f"Error fetching spare parts: {e}")
        import traceback
        traceback.print_exc()

    try:
        print("DEBUG: Rendering template")
        response = templates.TemplateResponse("dashboard_new.html", {
            "request": request,
            "kpis_report": latest_kpis_from_report,
            "latest_stored": latest_stored_results,
            "workorder_quality": workorder_quality,
            "recommendations": active_recommendations,
            "spare_parts": all_spare_parts,
            "current_user": current_user  # Pass the current_user to the template
        })
        print("DEBUG: Template rendered successfully")
        return response
    except Exception as e:
        print(f"DEBUG: Error rendering template: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering dashboard template: {str(e)}")

@app.get("/api/kpi/history/{kpi_name}", response_model=List[KpiHistoryPoint], tags=["KPIs"])
async def get_kpi_history(
    kpi_name: str = Path(..., min_length=3),
    # --- ADD Query Parameters for Date Range ---
    start_date: Optional[date] = Query(None, description="Filter history from this date (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="Filter history up to this date (YYYY-MM-DD)"),
    # --- END Query Parameters ---
    limit: int = 1000, # Increase default limit slightly or make it configurable
    db: Session = Depends(get_db),
    # Apply authentication - any authenticated user can view KPI history
    current_user: User = Depends(require_viewer_plus)
):
    """
    Retrieves the historical trend for a specified calculated KPI,
    optionally filtered by a date range.
    """
    try:
        # Log the authenticated user's request
        print(f"INFO: KPI history for {kpi_name} requested by user {current_user.email} (Role: {current_user.role})")

        # Start base query
        query = db.query(CalculatedKpiHistory).filter(
            CalculatedKpiHistory.kpi_name == kpi_name,
            CalculatedKpiHistory.kpi_value.isnot(None)
        )

        # --- ADD Date Filtering Logic ---
        if start_date:
            # Assuming calculation_timestamp is DateTime or Timestamp, cast start_date for comparison
            query = query.filter(CalculatedKpiHistory.calculation_timestamp >= start_date)
        if end_date:
            # Include the whole end date by comparing with the start of the next day
            query = query.filter(CalculatedKpiHistory.calculation_timestamp < (end_date + timedelta(days=1)))
            # Or if timestamp column is just Date: query = query.filter(CalculatedKpiHistory.calculation_timestamp <= end_date)
        # --- END Filtering Logic ---

        # Apply ordering and limit AFTER filtering
        history_data = query.order_by(
            asc(CalculatedKpiHistory.calculation_timestamp)
        ).limit(limit).all()

        return [KpiHistoryPoint(timestamp=item.calculation_timestamp, value=float(item.kpi_value) if item.kpi_value is not None else None) for item in history_data]
    except Exception as e: print(f"Error fetching KPI history for {kpi_name}: {e}"); raise HTTPException(status_code=500, detail=f"Error fetching history for {kpi_name}")

@app.post("/api/ingest/workorder", status_code=201, tags=["Ingestion"], summary="Ingest a single work order")
async def ingest_single_work_order(work_order_data: WorkOrderCreateAPI, db: Session = Depends(get_db), api_key: str = Depends(get_api_key)):
    try:
        wo_object = create_work_order_object(work_order_data.model_dump())
        db.add(wo_object)
        db.commit()
        db.refresh(wo_object)
        print(f"Successfully ingested work order via API, ID: {wo_object.workorderid}")
        return {"message": "Work order ingested successfully", "workorder_id": wo_object.workorderid}
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except SQLAlchemyError as dbe:
        db.rollback()
        print(f"API Ingestion DB Error: {dbe}")
        raise HTTPException(status_code=500, detail="Database error during ingestion.")
    except Exception as e:
        db.rollback()
        print(f"API Ingestion Error: {e}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred.")

@app.post("/upload/workorders", tags=["Ingestion"], summary="Upload work orders via CSV file")
async def upload_workorders_csv(_request: Request, file: UploadFile = File(...), db: Session = Depends(get_db)):
    # Basic security: Check if running locally or add proper auth
    # if "localhost" not in str(request.base_url) and "127.0.0.1" not in str(request.base_url):
    #     # Example: require API key even for upload if not local
    #     try: await get_api_key(request.headers.get(API_KEY_NAME) or "")
    #     except HTTPException as auth_exc: raise auth_exc # Re-raise auth error

    if not file.filename.endswith(('.csv', '.CSV')):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload a CSV.")

    try:
        df = pd.read_csv(file.file)
        df.columns = [c.lower().strip() for c in df.columns]
        required_columns = ['assetid', 'workordertype', 'description', 'status', 'assignedto', 'failurecode', 'failuretype',
                           'downtimeminutes', 'repairtimeminutes', 'maintenancecost', 'startdate', 'enddate']

        if not all(col in df.columns for col in required_columns):
            missing = [c for c in required_columns if c not in df.columns]
            raise HTTPException(status_code=400, detail=f"CSV missing: {missing}")

        work_orders_to_add = []
        row_errors = []
        api_key_map = {
            'assetid': 'assetId', 'workordertype': 'workOrderType', 'description': 'description', 'status': 'status',
            'assignedto': 'assignedTo', 'failurecode': 'failureCode', 'failuretype': 'failureType',
            'downtimeminutes': 'downtimeMinutes', 'repairtimeminutes': 'repairTimeMinutes',
            'maintenancecost': 'maintenanceCost', 'startdate': 'startDate', 'enddate': 'endDate'
        }

        for index, row in df.iterrows():
            try:
                processed_data = {}
                for key, value in row.to_dict().items():
                    api_key = api_key_map.get(key)
                    if api_key:
                        processed_data[api_key] = None if pd.isna(value) else value

                start_ts = pd.to_datetime(processed_data.get('startDate'), errors='coerce')
                end_ts = pd.to_datetime(processed_data.get('endDate'), errors='coerce')
                processed_data['startDate'] = start_ts.to_pydatetime(warn=False) if pd.notna(start_ts) else None
                processed_data['endDate'] = end_ts.to_pydatetime(warn=False) if pd.notna(end_ts) else None

                wo_object = create_work_order_object(processed_data)
                work_orders_to_add.append(wo_object)
            except Exception as row_error:
                print(f"Err processing CSV row {index+2}: {row_error}")
                row_errors.append(f"Row {index+2}: {row_error}")

        if not work_orders_to_add and row_errors:
            raise HTTPException(status_code=400, detail=f"All rows failed. First error: {row_errors[0]}")
        elif not work_orders_to_add:
            raise HTTPException(status_code=400, detail="No valid WOs found.")

        if work_orders_to_add:
            try:
                db.add_all(work_orders_to_add)
                db.commit()
                print(f"Added {len(work_orders_to_add)} WOs from CSV.")
            except SQLAlchemyError as db_e:
                db.rollback()
                print(f"DB err CSV insert: {db_e}")
                raise HTTPException(status_code=500, detail="DB error during CSV insert.")

        if row_errors:
            print(f"CSV upload complete with {len(row_errors)} errors.")

    except pd.errors.EmptyDataError:
        raise HTTPException(status_code=400, detail="CSV empty.")
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        db.rollback()
        print(f"ERROR processing CSV: {e}")
        raise HTTPException(status_code=500, detail=f"Failed processing CSV: {e}")
    finally:
        await file.close()

    # TODO: Pass success/error messages back to user via query params/flash
    return RedirectResponse(url="/", status_code=303)

# --- ADD API Endpoint for Recommendation Status Update ---
@app.patch("/api/recommendations/{recommendation_id}/status", status_code=200, tags=["Recommendations"])
async def update_recommendation_status(
    status_update: RecommendationStatusUpdate, # Request body with new status
    recommendation_id: int = Path(..., title="The ID of the recommendation to update", ge=1),
    db: Session = Depends(get_db),
    # Replace API key with Firebase auth - require manager or admin role
    current_user: User = Depends(require_manager_or_admin)
):
    """
    Updates the status of an inventory recommendation (e.g., to ACKNOWLEDGED or DISMISSED).
    """
    try:
        # Log the authenticated user's action
        print(f"INFO: Recommendation {recommendation_id} status update to {status_update.new_status} requested by user {current_user.email} (Role: {current_user.role})")

        # Find the existing recommendation
        recommendation = db.query(InventoryRecommendation).filter(
            InventoryRecommendation.id == recommendation_id
        ).first()

        if not recommendation:
            raise HTTPException(status_code=404, detail="Recommendation not found")

        # Check if status is already the desired status (optional)
        if recommendation.status == status_update.new_status:
            return {"message": f"Recommendation {recommendation_id} status already set to {status_update.new_status}"}

        # Update the status
        recommendation.status = status_update.new_status
        # last_updated_at should be handled by DB trigger or model's onupdate

        db.commit()
        print(f"Updated recommendation {recommendation_id} status to {status_update.new_status}")
        return {"message": f"Recommendation {recommendation_id} status updated to {status_update.new_status}"}

    except SQLAlchemyError as e:
        db.rollback()
        print(f"Error updating recommendation {recommendation_id} status: {e}")
        raise HTTPException(status_code=500, detail="Database error updating recommendation status.")
    except HTTPException as http_exc:
        # Re-raise HTTPExceptions (like 404 Not Found)
        raise http_exc
    except Exception as e:
        db.rollback()
        print(f"Unexpected error updating recommendation {recommendation_id} status: {e}")
        raise HTTPException(status_code=500, detail="Unexpected error updating recommendation status.")

# --- Authentication Test Endpoints ---
@app.get("/docs", response_class=HTMLResponse, include_in_schema=False)
async def custom_docs():
    """
    Serve the custom API documentation page
    """
    return FileResponse("static/api-docs.html")

@app.get("/login", response_class=HTMLResponse, tags=["Authentication"])
async def login_page(request: Request):
    """
    Serves the login page for testing Firebase Authentication.
    """
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/firebase-test", response_class=HTMLResponse, tags=["Authentication"])
async def firebase_test_page(request: Request):
    """
    Serves a simple page for testing Firebase Authentication directly.
    """
    return templates.TemplateResponse("firebase_test.html", {"request": request})

@app.get("/auth-debug", response_class=HTMLResponse, tags=["Authentication"])
async def auth_debug_page(request: Request):
    """
    Serves a debug page for testing authentication flow.
    """
    return templates.TemplateResponse("auth_debug.html", {"request": request})

@app.get("/kpi-reports", response_class=HTMLResponse, tags=["KPIs"])
async def kpi_reports_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the KPI Reports page.
    """
    try:
        # Get KPI reports data
        kpi_reports = db.query(KpiReport).order_by(desc(KpiReport.reportdate)).limit(10).all()

        # Get historical KPI data for charts
        kpi_history = {}
        for kpi_name in ['MTTR_Calculated', 'MTBF_Calculated', 'FailureRate_Calculated']:
            kpi_history[kpi_name] = db.query(CalculatedKpiHistory).filter(
                CalculatedKpiHistory.kpi_name == kpi_name
            ).order_by(desc(CalculatedKpiHistory.calculation_timestamp)).limit(30).all()

        return templates.TemplateResponse("kpi_reports.html", {
            "request": request,
            "kpi_reports": kpi_reports,
            "kpi_history": kpi_history,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering KPI Reports page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering KPI Reports page: {str(e)}")

@app.get("/inventory", response_class=HTMLResponse, tags=["Inventory"])
async def inventory_page(request: Request, part: Optional[int] = None, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Inventory page.
    """
    try:
        # Check if a specific part is requested
        if part is not None:
            # Get specific part details
            part_details = db.query(Sparepart).filter(Sparepart.partid == part).first()
            if not part_details:
                raise HTTPException(status_code=404, detail=f"Part with ID {part} not found")

            # Get part inventory analysis
            inventory_analysis = db.query(InventoryAnalysis).filter(InventoryAnalysis.part_id == part).first()

            # Get work orders that used this part (simplified for now)
            work_orders = db.query(WorkOrder).filter(WorkOrder.assetid.in_(
                db.query(Asset.assetid).filter(Asset.assettype.like(f'%{part_details.partname}%')).subquery()
            )).order_by(desc(WorkOrder.startdate)).limit(10).all()

            # Get assets that might use this part (simplified for now)
            assets = db.query(Asset).filter(Asset.assettype.like(f'%{part_details.partname}%')).limit(10).all()

            # Return part detail template
            return templates.TemplateResponse("part_detail.html", {
                "request": request,
                "part": part_details,
                "inventory_analysis": inventory_analysis,
                "work_orders": work_orders,
                "assets": assets,
                "current_user": current_user
            })
        else:
            # Get all spare parts data
            spare_parts = db.query(Sparepart).order_by(asc(Sparepart.partname)).all()

            # Get inventory metrics
            inventory_metrics = {
                "total_parts": len(spare_parts),
                "total_value": sum(part.stockquantity * part.unitprice for part in spare_parts if part.stockquantity is not None and part.unitprice is not None),
                "below_reorder": len([part for part in spare_parts if part.stockquantity is not None and part.reorderlevel is not None and part.stockquantity <= part.reorderlevel]),
                "class_a": len([part for part in spare_parts if part.abc_classification == 'A']),
                "class_b": len([part for part in spare_parts if part.abc_classification == 'B']),
                "class_c": len([part for part in spare_parts if part.abc_classification == 'C'])
            }

            # Return inventory list template
            return templates.TemplateResponse("inventory.html", {
                "request": request,
                "spare_parts": spare_parts,
                "inventory_metrics": inventory_metrics,
                "current_user": current_user
            })
    except Exception as e:
        print(f"Error rendering Inventory page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Inventory page: {str(e)}")

@app.get("/inventory/analysis", response_class=HTMLResponse, tags=["Inventory"])
async def inventory_analysis_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Inventory Analysis page.
    """
    try:
        return templates.TemplateResponse("inventory_analysis.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Inventory Analysis page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Inventory Analysis page: {str(e)}")

@app.get("/recommendations", response_class=HTMLResponse, tags=["Recommendations"])
async def recommendations_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Recommendations page.
    """
    try:
        # Get active recommendations
        active_recommendations = db.query(InventoryRecommendation, Sparepart.partid, Sparepart.partname, Sparepart.partnumber).join(
            Sparepart, InventoryRecommendation.part_id == Sparepart.partid
        ).filter(InventoryRecommendation.status == 'NEW').order_by(
            asc(InventoryRecommendation.priority), desc(InventoryRecommendation.generated_at)
        ).all()

        # Get historical recommendations
        historical_recommendations = db.query(InventoryRecommendation, Sparepart.partid, Sparepart.partname, Sparepart.partnumber).join(
            Sparepart, InventoryRecommendation.part_id == Sparepart.partid
        ).filter(InventoryRecommendation.status != 'NEW').order_by(
            desc(InventoryRecommendation.generated_at)
        ).limit(20).all()

        return templates.TemplateResponse("recommendations.html", {
            "request": request,
            "active_recommendations": active_recommendations,
            "historical_recommendations": historical_recommendations,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Recommendations page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Recommendations page: {str(e)}")

@app.get("/inventory-config", response_class=HTMLResponse, tags=["Inventory"])
async def inventory_config_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Inventory Configuration page. Requires ADMIN role for editing.
    """
    try:
        return templates.TemplateResponse("inventory_config.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Inventory Configuration page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Inventory Configuration page: {str(e)}")

@app.get("/admin", response_class=HTMLResponse, tags=["Admin"])
async def admin_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Admin page. Requires ADMIN role.
    """
    try:
        # Get users
        users = db.query(User).all()

        # Get system metrics
        system_metrics = {
            "total_users": len(users),
            "total_work_orders": db.query(func.count(WorkOrder.workorderid)).scalar(),
            "total_spare_parts": db.query(func.count(Sparepart.partid)).scalar(),
            "total_recommendations": db.query(func.count(InventoryRecommendation.id)).scalar(),
            "active_recommendations": db.query(func.count(InventoryRecommendation.id)).filter(InventoryRecommendation.status == 'NEW').scalar()
        }

        return templates.TemplateResponse("admin.html", {
            "request": request,
            "users": users,
            "system_metrics": system_metrics,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Admin page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Admin page: {str(e)}")

@app.get("/api/auth-test", tags=["Authentication"])
async def auth_test(current_user: User = Depends(get_current_user)):
    """
    Simple endpoint to test if authentication is working.
    Returns user info if authenticated.
    """
    return {
        "authenticated": True,
        "user_id": current_user.user_id,
        "email": current_user.email,
        "role": current_user.role,
        "message": "Authentication successful!"
    }

@app.get("/api/test-user-auth", tags=["Authentication"])
async def test_user_auth(token: str = 'test-admin-token', db: Session = Depends(get_db)):
    """
    Test endpoint to verify test user authentication is working.

    Args:
        token: The test user token (default: test-admin-token)
        db: The database session
    """
    # For testing purposes, we're enabling test authentication by default
    enable_test_auth = os.getenv("ENABLE_TEST_AUTH", "true").lower()
    if enable_test_auth not in ("true", "1", "yes"):
        raise HTTPException(status_code=404, detail="Test authentication is not enabled on this server.")

    try:
        # Import test user authentication module
        from test_user_auth import handle_test_user_token

        # Try to authenticate with the provided token
        user = handle_test_user_token(token, db)

        if user:
            return {
                "message": "Test user authentication successful",
                "user": user.email,
                "role": user.role,
                "user_id": user.user_id
            }
        else:
            return {
                "message": "Test user authentication failed",
                "error": "User not found for token"
            }
    except Exception as e:
        return {
            "message": "Test user authentication failed",
            "error": str(e)
        }

@app.get("/assets", response_class=HTMLResponse, tags=["Assets"])
async def assets_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Assets page.
    """
    try:
        # Get all assets
        assets = db.query(Asset).order_by(asc(Asset.assetname)).all()

        # Get asset locations, systems, and categories for filtering
        asset_locations = db.query(AssetLocation).order_by(asc(AssetLocation.location_name)).all()
        asset_systems = db.query(AssetSystem).order_by(asc(AssetSystem.system_name)).all()
        asset_categories = db.query(AssetCategory).order_by(asc(AssetCategory.category_name)).all()

        return templates.TemplateResponse("assets.html", {
            "request": request,
            "assets": assets,
            "asset_locations": asset_locations,
            "asset_systems": asset_systems,
            "asset_categories": asset_categories,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Assets page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Assets page: {str(e)}")

@app.get("/assets/{asset_id}", response_class=HTMLResponse, tags=["Assets"])
async def asset_detail(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset Detail page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Get asset location, system, and category
        location = None
        if asset.location_id:
            location = db.query(AssetLocation).filter(AssetLocation.location_id == asset.location_id).first()

        system = None
        if asset.system_id:
            system = db.query(AssetSystem).filter(AssetSystem.system_id == asset.system_id).first()

        category = None
        if asset.category_id:
            category = db.query(AssetCategory).filter(AssetCategory.category_id == asset.category_id).first()

        # Get recent work orders for this asset
        recent_work_orders = db.query(WorkOrder).filter(WorkOrder.assetid == asset_id).order_by(desc(WorkOrder.startdate)).limit(5).all()

        # Get upcoming PM schedules
        now = datetime.now()
        upcoming_pm = db.query(PMSchedule).filter(
            PMSchedule.asset_id == asset_id,
            PMSchedule.next_due_date >= now
        ).order_by(asc(PMSchedule.next_due_date)).limit(5).all()

        # Calculate asset health (simplified example)
        asset_health = random.randint(60, 100)  # Replace with actual calculation
        health_last_updated = datetime.now()

        # Calculate key metrics (simplified examples)
        mtbf = random.uniform(30, 90)  # Replace with actual calculation
        mttr = random.uniform(2, 24)  # Replace with actual calculation
        availability = random.uniform(85, 99.9)  # Replace with actual calculation
        reliability = random.uniform(80, 99)  # Replace with actual calculation

        return templates.TemplateResponse("asset_detail.html", {
            "request": request,
            "asset": asset,
            "location": location,
            "system": system,
            "category": category,
            "recent_work_orders": recent_work_orders,
            "upcoming_pm": upcoming_pm,
            "now": now,
            "asset_health": asset_health,
            "health_last_updated": health_last_updated,
            "mtbf": mtbf,
            "mttr": mttr,
            "availability": availability,
            "reliability": reliability,
            "current_user": current_user
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset Detail page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset Detail page: {str(e)}")

@app.get("/assets/{asset_id}/kpi", response_class=HTMLResponse, tags=["Assets"])
async def asset_kpi(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset KPI page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Placeholder for KPI data
        # In a real implementation, you would fetch actual KPI data for this asset

        return templates.TemplateResponse("asset_kpi.html", {
            "request": request,
            "asset": asset,
            "current_user": current_user
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset KPI page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset KPI page: {str(e)}")

@app.get("/assets/{asset_id}/maintenance", response_class=HTMLResponse, tags=["Assets"])
async def asset_maintenance(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset Maintenance History page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Get work orders for this asset
        work_orders = db.query(WorkOrder).filter(WorkOrder.assetid == asset_id).order_by(desc(WorkOrder.startdate)).all()

        return templates.TemplateResponse("asset_maintenance.html", {
            "request": request,
            "asset": asset,
            "work_orders": work_orders,
            "current_user": current_user
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset Maintenance page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset Maintenance page: {str(e)}")

@app.get("/assets/{asset_id}/specifications", response_class=HTMLResponse, tags=["Assets"])
async def asset_specifications(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset Specifications page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Get asset specifications
        specifications = db.query(AssetSpecification).filter(AssetSpecification.asset_id == asset_id).all()

        # Get asset warranty information
        warranties = db.query(AssetWarranty).filter(AssetWarranty.asset_id == asset_id).all()

        return templates.TemplateResponse("asset_specifications.html", {
            "request": request,
            "asset": asset,
            "specifications": specifications,
            "warranties": warranties,
            "current_user": current_user,
            "now": datetime.now()
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset Specifications page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset Specifications page: {str(e)}")

@app.get("/assets/{asset_id}/documents", response_class=HTMLResponse, tags=["Assets"])
async def asset_documents(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset Documents page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Get asset documents
        documents = db.query(TechnicalDocument).join(
            AssetDocument, TechnicalDocument.document_id == AssetDocument.document_id
        ).filter(AssetDocument.asset_id == asset_id).all()

        return templates.TemplateResponse("asset_documents.html", {
            "request": request,
            "asset": asset,
            "documents": documents,
            "current_user": current_user
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset Documents page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset Documents page: {str(e)}")

@app.get("/assets/{asset_id}/meters", response_class=HTMLResponse, tags=["Assets"])
async def asset_meters(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset Meters page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Get asset meters
        meters = db.query(AssetMeter).filter(AssetMeter.asset_id == asset_id).all()

        # Get meter readings for each meter
        meter_readings = {}
        for meter in meters:
            readings = db.query(MeterReading).filter(
                MeterReading.meter_id == meter.meter_id
            ).order_by(desc(MeterReading.reading_date)).limit(10).all()
            meter_readings[meter.meter_id] = readings

        return templates.TemplateResponse("asset_meters.html", {
            "request": request,
            "asset": asset,
            "meters": meters,
            "meter_readings": meter_readings,
            "current_user": current_user
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset Meters page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset Meters page: {str(e)}")

@app.get("/assets/{asset_id}/pm", response_class=HTMLResponse, tags=["Assets"])
async def asset_pm(asset_id: int, request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset PM Schedules page.
    """
    try:
        # Get asset details
        asset = db.query(Asset).filter(Asset.assetid == asset_id).first()
        if not asset:
            raise HTTPException(status_code=404, detail=f"Asset with ID {asset_id} not found")

        # Get PM schedules for this asset
        pm_schedules = db.query(PMSchedule).filter(PMSchedule.asset_id == asset_id).all()

        # Get job plans for each schedule
        job_plans = {}
        for schedule in pm_schedules:
            plans = db.query(PMJobPlan).filter(PMJobPlan.schedule_id == schedule.schedule_id).all()
            job_plans[schedule.schedule_id] = plans

        # Get tasks for each job plan
        job_tasks = {}
        for schedule_id, plans in job_plans.items():
            for plan in plans:
                tasks = db.query(PMJobTask).filter(PMJobTask.plan_id == plan.plan_id).order_by(asc(PMJobTask.sequence_number)).all()
                job_tasks[plan.plan_id] = tasks

        return templates.TemplateResponse("asset_pm.html", {
            "request": request,
            "asset": asset,
            "pm_schedules": pm_schedules,
            "job_plans": job_plans,
            "job_tasks": job_tasks,
            "now": datetime.now(),
            "current_user": current_user
        })
    except HTTPException as http_e:
        raise http_e
    except Exception as e:
        print(f"Error rendering Asset PM Schedules page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Asset PM Schedules page: {str(e)}")

# --- END API Endpoint ---

# --- ADD User Management Endpoints ---

# --- ADD Inventory Visualization Endpoints ---
@app.get("/inventory-visualization", response_class=HTMLResponse, tags=["Inventory"])
async def inventory_visualization_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Inventory Visualization page. Any authenticated user can view.
    """
    try:
        return templates.TemplateResponse(
            "inventory_visualization.html",
            {"request": request, "current_user": current_user}
        )
    except Exception as e:
        print(f"Error rendering Inventory Visualization page: {e}")
        raise HTTPException(status_code=500, detail=f"Error rendering Inventory Visualization page: {str(e)}")

# --- Chart Test Page ---
@app.get("/chart-test", response_class=HTMLResponse, tags=["Test"])
async def chart_test_page(request: Request):
    """
    Serves a test page for Chart.js.
    """
    return templates.TemplateResponse("chart_test.html", {"request": request})

# --- ADD Asset Performance Visualization Endpoints ---
@app.get("/kpi-analytics", response_class=HTMLResponse, tags=["Analytics"])
async def kpi_analytics_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the KPI Analytics page. Any authenticated user can view.
    """
    try:
        return templates.TemplateResponse(
            "kpi_analytics_fixed.html",
            {"request": request, "current_user": current_user}
        )
    except Exception as e:
        print(f"Error rendering KPI Analytics page: {e}")
        raise HTTPException(status_code=500, detail=f"Error rendering KPI Analytics page: {str(e)}")

@app.get("/asset-performance", response_class=HTMLResponse, tags=["Assets"])
async def asset_performance_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Asset Performance Visualization page. Any authenticated user can view.
    """
    try:
        return templates.TemplateResponse(
            "asset_performance.html",
            {"request": request, "current_user": current_user}
        )
    except Exception as e:
        print(f"Error rendering Asset Performance page: {e}")
        raise HTTPException(status_code=500, detail=f"Error rendering Asset Performance page: {str(e)}")

@app.get("/api/assets/performance", tags=["Assets"])
async def get_asset_performance_data(
    chart_type: str = Query("oee", description="Type of chart to display"),
    asset_group: str = Query("all", description="Asset group to filter by"),
    date_from: Optional[str] = Query(None, description="Start date for filtering (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="End date for filtering (YYYY-MM-DD)"),
    comparison_period: str = Query("none", description="Comparison period type"),
    aggregation: str = Query("daily", description="Data aggregation level"),
    limit: str = Query("10", description="Number of assets to return"),
    db: Session = Depends(get_db),
    _current_user: User = Depends(require_viewer_plus)  # Prefix with underscore to indicate it's intentionally unused
):
    """
    Get asset performance data for visualization. Any authenticated user can view.
    """
    try:
        # This is a placeholder implementation that generates sample data
        # In a real implementation, you would fetch actual data from the database

        # Generate time labels based on aggregation and date range
        time_labels = []
        main_chart_data = []
        comparison_data = []

        # Default date range (last 30 days) if not specified
        end_date = datetime.now().date()
        if date_to:
            try:
                end_date = datetime.strptime(date_to, "%Y-%m-%d").date()
            except ValueError:
                print(f"Invalid date_to format: {date_to}")

        start_date = end_date - timedelta(days=30)
        if date_from:
            try:
                start_date = datetime.strptime(date_from, "%Y-%m-%d").date()
            except ValueError:
                print(f"Invalid date_from format: {date_from}")

        # Generate time labels and data points based on aggregation
        current_date = start_date
        while current_date <= end_date:
            if aggregation == "daily":
                time_labels.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)
            elif aggregation == "weekly":
                time_labels.append(f"Week {current_date.isocalendar()[1]}")
                current_date += timedelta(days=7)
            elif aggregation == "monthly":
                time_labels.append(current_date.strftime("%b %Y"))
                # Move to the next month
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            elif aggregation == "quarterly":
                quarter = (current_date.month - 1) // 3 + 1
                time_labels.append(f"Q{quarter} {current_date.year}")
                # Move to the next quarter
                if current_date.month >= 10:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 3)

        # Generate random data for the main chart based on chart type
        for _ in time_labels:
            if chart_type == "oee":
                main_chart_data.append(round(random.uniform(60, 95), 1))
            elif chart_type == "mttr":
                main_chart_data.append(round(random.uniform(1, 24), 2))
            elif chart_type == "mtbf":
                main_chart_data.append(round(random.uniform(100, 500), 2))
            elif chart_type == "availability":
                main_chart_data.append(round(random.uniform(70, 99), 1))
            elif chart_type == "performance":
                main_chart_data.append(round(random.uniform(65, 95), 1))
            elif chart_type == "quality":
                main_chart_data.append(round(random.uniform(80, 99.9), 1))

        # Generate comparison data if requested
        if comparison_period != "none":
            for _ in time_labels:
                if chart_type == "oee":
                    comparison_data.append(round(random.uniform(55, 90), 1))
                elif chart_type == "mttr":
                    comparison_data.append(round(random.uniform(2, 30), 2))
                elif chart_type == "mtbf":
                    comparison_data.append(round(random.uniform(80, 450), 2))
                elif chart_type == "availability":
                    comparison_data.append(round(random.uniform(65, 95), 1))
                elif chart_type == "performance":
                    comparison_data.append(round(random.uniform(60, 90), 1))
                elif chart_type == "quality":
                    comparison_data.append(round(random.uniform(75, 99), 1))

        # Get asset data for comparison chart and table
        assets = []
        asset_names = []
        comparison_chart_data = []

        # Query assets based on the asset group filter
        asset_query = db.query(Asset)

        if asset_group == "critical":
            asset_query = asset_query.filter(Asset.criticality == "Critical")
        elif asset_group == "production":
            asset_query = asset_query.filter(Asset.assettype == "Production")
        elif asset_group == "utility":
            asset_query = asset_query.filter(Asset.assettype == "Utility")

        # Apply limit
        if limit != "all":
            asset_query = asset_query.limit(int(limit))

        # Execute query
        db_assets = asset_query.all()

        # Generate performance data for each asset
        for asset in db_assets:
            asset_names.append(asset.assetname)

            # Generate random performance metric based on chart type
            if chart_type == "oee":
                metric_value = round(random.uniform(60, 95), 1)
            elif chart_type == "mttr":
                metric_value = round(random.uniform(1, 24), 2)
            elif chart_type == "mtbf":
                metric_value = round(random.uniform(100, 500), 2)
            elif chart_type == "availability":
                metric_value = round(random.uniform(70, 99), 1)
            elif chart_type == "performance":
                metric_value = round(random.uniform(65, 95), 1)
            elif chart_type == "quality":
                metric_value = round(random.uniform(80, 99.9), 1)

            comparison_chart_data.append(metric_value)

            # Generate complete asset data for the table
            oee = round(random.uniform(60, 95), 1)
            availability = round(random.uniform(70, 99), 1)
            performance = round(random.uniform(65, 95), 1)
            quality = round(random.uniform(80, 99.9), 1)
            mttr = round(random.uniform(1, 24), 2)
            mtbf = round(random.uniform(100, 500), 2)
            downtime = round(random.uniform(10, 100), 2)

            # Determine status based on OEE
            if oee > 85:
                status = "Running"
            elif oee > 70:
                status = "Idle"
            elif oee > 50:
                status = "Maintenance"
            else:
                status = "Down"

            assets.append({
                "asset_id": asset.assetid,
                "asset_name": asset.assetname,
                "oee": oee,
                "availability": availability,
                "performance": performance,
                "quality": quality,
                "mttr": mttr,
                "mtbf": mtbf,
                "downtime": downtime,
                "status": status
            })

        # Generate distribution data
        distribution_labels = []
        distribution_data = []

        if chart_type == "oee":
            distribution_labels = ["Excellent (>90%)", "Good (80-90%)", "Average (70-80%)", "Poor (60-70%)", "Critical (<60%)"]
            distribution_data = [random.randint(5, 15), random.randint(10, 20), random.randint(15, 25), random.randint(5, 15), random.randint(1, 10)]
        elif chart_type == "mttr":
            distribution_labels = ["<2 hrs", "2-5 hrs", "5-10 hrs", "10-24 hrs", ">24 hrs"]
            distribution_data = [random.randint(10, 20), random.randint(15, 25), random.randint(10, 20), random.randint(5, 15), random.randint(1, 5)]
        elif chart_type == "mtbf":
            distribution_labels = [">400 hrs", "300-400 hrs", "200-300 hrs", "100-200 hrs", "<100 hrs"]
            distribution_data = [random.randint(5, 15), random.randint(10, 20), random.randint(15, 25), random.randint(10, 20), random.randint(1, 10)]
        elif chart_type in ["availability", "performance", "quality"]:
            distribution_labels = ["Excellent (>95%)", "Good (90-95%)", "Average (80-90%)", "Poor (70-80%)", "Critical (<70%)"]
            distribution_data = [random.randint(5, 15), random.randint(10, 20), random.randint(15, 25), random.randint(5, 15), random.randint(1, 10)]

        # Return the complete data structure
        return {
            "timeLabels": time_labels,
            "mainChartData": main_chart_data,
            "comparisonData": comparison_data,
            "assetNames": asset_names,
            "comparisonChartData": comparison_chart_data,
            "distributionLabels": distribution_labels,
            "distributionData": distribution_data,
            "assets": assets
        }
    except Exception as e:
        print(f"Error fetching asset performance data: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error fetching asset performance data: {str(e)}")

@app.get("/api/inventory/visualization", tags=["Inventory"])
async def get_inventory_visualization_data(
    _chart_type: str = Query("stockLevels", description="Type of chart to display"),  # Prefix with underscore to indicate it's intentionally unused
    sort_by: str = Query("name", description="Field to sort by"),
    filter_by: str = Query("all", description="Filter criteria"),
    limit: str = Query("10", description="Number of items to return"),
    date_from: Optional[str] = Query(None, description="Start date for filtering (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="End date for filtering (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    _current_user: User = Depends(require_viewer_plus)  # Prefix with underscore to indicate it's intentionally unused
):
    """
    Get inventory data for visualization. Any authenticated user can view.
    """
    try:
        # Get all parts with their inventory analysis
        parts_query = db.query(
            Sparepart.partid.label("part_id"),
            Sparepart.partname.label("part_name"),
            Sparepart.partnumber.label("part_number"),
            Sparepart.stockquantity.label("current_stock"),
            Sparepart.unitprice.label("unit_price"),
            Sparepart.abc_classification,
            InventoryAnalysis.optimal_stock,
            InventoryAnalysis.stock_difference,
            InventoryAnalysis.current_cost.label("current_value"),
            InventoryAnalysis.optimal_cost.label("optimal_value"),
            InventoryAnalysis.potential_savings,
            InventoryAnalysis.days_of_supply,
            InventoryAnalysis.stockout_risk
        ).join(
            InventoryAnalysis,
            Sparepart.partid == InventoryAnalysis.part_id,
            isouter=True
        )

        # Apply filters
        if filter_by == "overstock":
            parts_query = parts_query.filter(InventoryAnalysis.stock_difference < 0)
        elif filter_by == "understock":
            parts_query = parts_query.filter(InventoryAnalysis.stock_difference > 0)
        elif filter_by == "critical":
            parts_query = parts_query.filter(InventoryAnalysis.stockout_risk > 25)
        elif filter_by == "highValue":
            parts_query = parts_query.filter(Sparepart.unitprice * Sparepart.stockquantity > 500)

        # Apply date range filters if provided
        if date_from:
            try:
                from_date = datetime.strptime(date_from, "%Y-%m-%d").date()
                parts_query = parts_query.filter(InventoryAnalysis.analysis_date >= from_date)
            except ValueError:
                print(f"Invalid date_from format: {date_from}")

        if date_to:
            try:
                to_date = datetime.strptime(date_to, "%Y-%m-%d").date()
                # Add one day to include the end date in the range
                to_date = to_date + timedelta(days=1)
                parts_query = parts_query.filter(InventoryAnalysis.analysis_date < to_date)
            except ValueError:
                print(f"Invalid date_to format: {date_to}")

        # Apply sorting
        if sort_by == "name":
            parts_query = parts_query.order_by(Sparepart.partname)
        elif sort_by == "stock":
            parts_query = parts_query.order_by(desc(Sparepart.stockquantity))
        elif sort_by == "value":
            parts_query = parts_query.order_by(desc(InventoryAnalysis.current_cost))
        elif sort_by == "savings":
            parts_query = parts_query.order_by(desc(InventoryAnalysis.potential_savings))
        elif sort_by == "risk":
            parts_query = parts_query.order_by(desc(InventoryAnalysis.stockout_risk))

        # Apply limit
        if limit != "all":
            parts_query = parts_query.limit(int(limit))

        # Execute query
        parts = parts_query.all()

        # Convert to list of dictionaries
        result = []
        for part in parts:
            part_dict = {
                "part_id": part.part_id,
                "part_name": part.part_name,
                "part_number": part.part_number,
                "current_stock": part.current_stock or 0,
                "unit_price": float(part.unit_price or 0),
                "abc_classification": part.abc_classification,
                "optimal_stock": part.optimal_stock or 0,
                "stock_difference": part.stock_difference or 0,
                "current_value": float(part.current_value or 0),
                "optimal_value": float(part.optimal_value or 0),
                "potential_savings": float(part.potential_savings or 0),
                "days_of_supply": part.days_of_supply or 0,
                "stockout_risk": float(part.stockout_risk or 0)
            }
            result.append(part_dict)

        return result
    except Exception as e:
        print(f"Error fetching inventory visualization data: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching inventory visualization data: {str(e)}")

# --- Inventory Analysis Endpoints ---
@app.get("/api/inventory/analysis", tags=["Inventory"], response_model=List[dict])
async def get_inventory_analysis(db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get inventory analysis data for all parts. Any authenticated user can view.
    """
    try:
        # Get the latest analysis for each part
        subquery = db.query(
            InventoryAnalysis.part_id,
            func.max(InventoryAnalysis.analysis_date).label('latest_date')
        ).group_by(InventoryAnalysis.part_id).subquery('latest_analysis')

        latest_analyses = db.query(InventoryAnalysis).join(
            subquery,
            and_(
                InventoryAnalysis.part_id == subquery.c.part_id,
                InventoryAnalysis.analysis_date == subquery.c.latest_date
            )
        ).all()

        # Join with spare parts data
        result = []
        for analysis in latest_analyses:
            part = db.query(Sparepart).filter(Sparepart.partid == analysis.part_id).first()
            if part:
                result.append({
                    "part_id": analysis.part_id,
                    "part_name": part.partname,
                    "part_number": part.partnumber,
                    "current_stock": analysis.current_stock,
                    "optimal_stock": analysis.optimal_stock,
                    "stock_difference": analysis.stock_difference,
                    "current_cost": float(analysis.current_cost),
                    "optimal_cost": float(analysis.optimal_cost),
                    "potential_savings": float(analysis.potential_savings),
                    "days_of_supply": analysis.days_of_supply,
                    "stockout_risk": float(analysis.stockout_risk),
                    "abc_classification": part.abc_classification,
                    "analysis_date": analysis.analysis_date
                })

        # Sort by potential savings (descending)
        result.sort(key=lambda x: x["potential_savings"], reverse=True)
        return result
    except Exception as e:
        print(f"Error fetching inventory analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching inventory analysis: {str(e)}")

@app.get("/api/inventory/analysis/{part_id}", tags=["Inventory"])
async def get_part_inventory_analysis(part_id: int, db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get detailed inventory analysis data for a specific part. Any authenticated user can view.
    """
    try:
        # Get the part
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if not part:
            raise HTTPException(status_code=404, detail=f"Part with ID {part_id} not found")

        # Get the latest analysis
        analysis = db.query(InventoryAnalysis).filter(
            InventoryAnalysis.part_id == part_id
        ).order_by(desc(InventoryAnalysis.analysis_date)).first()

        # Get the latest EOQ calculation
        eoq_calc = db.query(EOQCalculation).filter(
            EOQCalculation.part_id == part_id
        ).order_by(desc(EOQCalculation.calculated_at)).first()

        # Get the latest safety stock calculation
        ss_calc = db.query(SafetyStockCalculation).filter(
            SafetyStockCalculation.part_id == part_id
        ).order_by(desc(SafetyStockCalculation.calculated_at)).first()

        # Build the response
        result = {
            "part": {
                "part_id": part.partid,
                "part_name": part.partname,
                "part_number": part.partnumber,
                "manufacturer": part.manufacturer,
                "stock_quantity": part.stockquantity,
                "unit_price": float(part.unitprice) if part.unitprice else None,
                "abc_classification": part.abc_classification,
                "last_restocked": part.lastrestocked,
                "avg_monthly_consumption": float(part.avg_monthly_consumption) if part.avg_monthly_consumption else None
            }
        }

        if analysis:
            result["analysis"] = {
                "current_stock": analysis.current_stock,
                "optimal_stock": analysis.optimal_stock,
                "stock_difference": analysis.stock_difference,
                "current_cost": float(analysis.current_cost),
                "optimal_cost": float(analysis.optimal_cost),
                "potential_savings": float(analysis.potential_savings),
                "days_of_supply": analysis.days_of_supply,
                "stockout_risk": float(analysis.stockout_risk),
                "analysis_date": analysis.analysis_date
            }

        if eoq_calc:
            result["eoq"] = {
                "annual_demand": float(eoq_calc.annual_demand) if eoq_calc.annual_demand else None,
                "ordering_cost": float(eoq_calc.ordering_cost),
                "holding_cost": float(eoq_calc.holding_cost),
                "eoq_value": float(eoq_calc.eoq_value),
                "annual_ordering_cost": float(eoq_calc.annual_ordering_cost),
                "annual_holding_cost": float(eoq_calc.annual_holding_cost),
                "total_annual_cost": float(eoq_calc.total_annual_cost),
                "optimal_order_frequency": eoq_calc.optimal_order_frequency,
                "calculated_at": eoq_calc.calculated_at
            }

        if ss_calc:
            result["safety_stock"] = {
                "avg_daily_demand": float(ss_calc.avg_daily_demand) if ss_calc.avg_daily_demand else None,
                "lead_time_days": ss_calc.lead_time_days,
                "demand_variability": float(ss_calc.demand_variability),
                "lead_time_variability": float(ss_calc.lead_time_variability),
                "service_level": float(ss_calc.service_level),
                "safety_stock_value": float(ss_calc.safety_stock_value),
                "reorder_point": float(ss_calc.reorder_point),
                "calculated_at": ss_calc.calculated_at
            }

        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching part inventory analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching part inventory analysis: {str(e)}")

# --- KPI History Endpoint ---
@app.get("/api/kpi/history/{kpi_name}", tags=["Analytics"])
async def get_kpi_history(
    kpi_name: str,
    limit: int = Query(100, description="Maximum number of data points to return"),
    start_date: Optional[str] = Query(None, description="Start date for filtering (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date for filtering (YYYY-MM-DD)"),
    asset_id: Optional[int] = Query(None, description="Filter by asset ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_viewer_plus)
):
    """
    Get historical data for a specific KPI. Any authenticated user can view.
    """
    try:
        # Parse date filters if provided
        start_date_obj = None
        end_date_obj = None

        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            except ValueError:
                print(f"Invalid start_date format: {start_date}")
                raise HTTPException(status_code=400, detail="Invalid start date format. Use YYYY-MM-DD.")

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                # Add one day to include the end date in the range
                end_date_obj = datetime.combine(end_date_obj, time.max)
            except ValueError:
                print(f"Invalid end_date format: {end_date}")
                raise HTTPException(status_code=400, detail="Invalid end date format. Use YYYY-MM-DD.")

        # Build query
        query = db.query(KPI).filter(KPI.kpi_name == kpi_name)

        # Apply filters
        if start_date_obj:
            query = query.filter(KPI.calculation_timestamp >= start_date_obj)

        if end_date_obj:
            query = query.filter(KPI.calculation_timestamp <= end_date_obj)

        if asset_id:
            query = query.filter(KPI.asset_id == asset_id)

        # Order by timestamp and limit results
        query = query.order_by(asc(KPI.calculation_timestamp)).limit(limit)

        # Execute query
        kpi_history = query.all()

        # Format results
        result = [
            {
                "timestamp": kpi.calculation_timestamp.isoformat(),
                "value": float(kpi.kpi_value) if kpi.kpi_value is not None else None,
                "source": kpi.calculation_source,
                "asset_id": kpi.asset_id
            } for kpi in kpi_history
        ]

        # If no data is found, return sample data for demonstration
        if not result:
            # Generate sample data for the last 30 days
            today = datetime.now()
            sample_data = []

            # Generate different sample data based on KPI type
            if kpi_name == "MTTR_Calculated":
                base_value = 4.5  # hours
                for i in range(30):
                    date = today - timedelta(days=i)
                    # Add some randomness to the data
                    value = base_value + (random.random() - 0.5) * 2
                    sample_data.append({
                        "timestamp": date.isoformat(),
                        "value": value,
                        "source": "sample",
                        "asset_id": asset_id or 1
                    })
            elif kpi_name == "MTBF_Calculated":
                base_value = 350.0  # hours
                for i in range(30):
                    date = today - timedelta(days=i)
                    # Add some randomness to the data
                    value = base_value + (random.random() - 0.5) * 50
                    sample_data.append({
                        "timestamp": date.isoformat(),
                        "value": value,
                        "source": "sample",
                        "asset_id": asset_id or 1
                    })
            elif kpi_name == "FailureRate_Calculated":
                base_value = 6.0  # failures per year
                for i in range(30):
                    date = today - timedelta(days=i)
                    # Add some randomness to the data
                    value = base_value + (random.random() - 0.5) * 2
                    sample_data.append({
                        "timestamp": date.isoformat(),
                        "value": value,
                        "source": "sample",
                        "asset_id": asset_id or 1
                    })
            else:
                # Default sample data for other KPIs
                base_value = 50.0
                for i in range(30):
                    date = today - timedelta(days=i)
                    # Add some randomness to the data
                    value = base_value + (random.random() - 0.5) * 10
                    sample_data.append({
                        "timestamp": date.isoformat(),
                        "value": value,
                        "source": "sample",
                        "asset_id": asset_id or 1
                    })

            # Sort by timestamp
            sample_data.sort(key=lambda x: x["timestamp"])
            return sample_data

        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching KPI history: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error fetching KPI history: {str(e)}")

# --- KPI Dashboard History Endpoint (No Auth) ---
@app.get("/api/dashboard/kpi/history/{kpi_name}", tags=["Dashboard"])
async def get_dashboard_kpi_history(
    kpi_name: str,
    limit: int = Query(100, description="Maximum number of data points to return"),
    start_date: Optional[str] = Query(None, description="Start date for filtering (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date for filtering (YYYY-MM-DD)"),
    asset_id: Optional[int] = Query(None, description="Filter by asset ID"),
    db: Session = Depends(get_db)
):
    """
    Get historical data for a specific KPI. No authentication required for dashboard use.
    """
    try:
        # Generate sample data for the last 30 days
        today = datetime.now()
        sample_data = []

        # Generate different sample data based on KPI type
        if kpi_name == "MTTR_Calculated":
            base_value = 4.5  # hours
            for i in range(30):
                date = today - timedelta(days=i)
                # Add some randomness to the data
                value = base_value + (random.random() - 0.5) * 2
                sample_data.append({
                    "timestamp": date.isoformat(),
                    "value": value,
                    "source": "sample",
                    "asset_id": asset_id or 1
                })
        elif kpi_name == "MTBF_Calculated":
            base_value = 350.0  # hours
            for i in range(30):
                date = today - timedelta(days=i)
                # Add some randomness to the data
                value = base_value + (random.random() - 0.5) * 50
                sample_data.append({
                    "timestamp": date.isoformat(),
                    "value": value,
                    "source": "sample",
                    "asset_id": asset_id or 1
                })
        elif kpi_name == "FailureRate_Calculated":
            base_value = 6.0  # failures per year
            for i in range(30):
                date = today - timedelta(days=i)
                # Add some randomness to the data
                value = base_value + (random.random() - 0.5) * 2
                sample_data.append({
                    "timestamp": date.isoformat(),
                    "value": value,
                    "source": "sample",
                    "asset_id": asset_id or 1
                })
        else:
            # Default sample data for other KPIs
            base_value = 50.0
            for i in range(30):
                date = today - timedelta(days=i)
                # Add some randomness to the data
                value = base_value + (random.random() - 0.5) * 10
                sample_data.append({
                    "timestamp": date.isoformat(),
                    "value": value,
                    "source": "sample",
                    "asset_id": asset_id or 1
                })

        # Sort by timestamp
        sample_data.sort(key=lambda x: x["timestamp"])
        return sample_data
    except Exception as e:
        print(f"Error generating dashboard KPI history: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error generating dashboard KPI history: {str(e)}")

# --- KPI Analytics Endpoints ---
@app.get("/api/kpi/analytics", tags=["Analytics"])
async def get_kpi_analytics(
    kpi_type: str = Query("oee", description="Type of KPI to analyze"),
    asset_category: str = Query("all", description="Asset category to filter by"),
    date_from: Optional[str] = Query(None, description="Start date for filtering (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="End date for filtering (YYYY-MM-DD)"),
    time_aggregation: str = Query("monthly", description="Time aggregation level"),
    anomaly_detection: str = Query("auto", description="Anomaly detection method"),
    benchmark: str = Query("none", description="Benchmark to compare against"),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """
    Get KPI analytics data for visualization. Any authenticated user can view.
    """
    try:
        # This is a placeholder implementation that generates sample data
        # In a real implementation, you would fetch actual data from the database

        # Generate time labels based on aggregation and date range
        time_labels = []
        kpi_data = []
        benchmark_data = []

        # Default date range (last 12 months) if not specified
        end_date = datetime.now().date()
        if date_to:
            try:
                end_date = datetime.strptime(date_to, "%Y-%m-%d").date()
            except ValueError:
                print(f"Invalid date_to format: {date_to}")

        start_date = end_date - timedelta(days=365)  # Default to 1 year
        if date_from:
            try:
                start_date = datetime.strptime(date_from, "%Y-%m-%d").date()
            except ValueError:
                print(f"Invalid date_from format: {date_from}")

        # Generate time labels and data points based on aggregation
        current_date = start_date
        while current_date <= end_date:
            if time_aggregation == "daily":
                time_labels.append(current_date.strftime("%Y-%m-%d"))
                current_date += timedelta(days=1)
            elif time_aggregation == "weekly":
                time_labels.append(f"Week {current_date.isocalendar()[1]}, {current_date.year}")
                current_date += timedelta(days=7)
            elif time_aggregation == "monthly":
                time_labels.append(current_date.strftime("%b %Y"))
                # Move to the next month
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
            elif time_aggregation == "quarterly":
                quarter = (current_date.month - 1) // 3 + 1
                time_labels.append(f"Q{quarter} {current_date.year}")
                # Move to the next quarter
                if current_date.month >= 10:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 3)

        # Generate random data for the main chart based on chart type
        for _ in time_labels:
            if kpi_type == "oee":
                kpi_data.append(round(random.uniform(60, 95), 1))
            elif kpi_type == "mttr":
                kpi_data.append(round(random.uniform(1, 24), 2))
            elif kpi_type == "mtbf":
                kpi_data.append(round(random.uniform(100, 500), 2))
            elif kpi_type == "availability":
                kpi_data.append(round(random.uniform(70, 99), 1))
            elif kpi_type == "performance":
                kpi_data.append(round(random.uniform(65, 95), 1))
            elif kpi_type == "quality":
                kpi_data.append(round(random.uniform(80, 99.9), 1))

        # Generate benchmark data if requested
        if benchmark != "none":
            for _ in time_labels:
                if kpi_type == "oee":
                    benchmark_data.append(round(random.uniform(55, 90), 1))
                elif kpi_type == "mttr":
                    benchmark_data.append(round(random.uniform(2, 30), 2))
                elif kpi_type == "mtbf":
                    benchmark_data.append(round(random.uniform(80, 450), 2))
                elif kpi_type == "availability":
                    benchmark_data.append(round(random.uniform(65, 95), 1))
                elif kpi_type == "performance":
                    benchmark_data.append(round(random.uniform(60, 90), 1))
                elif kpi_type == "quality":
                    benchmark_data.append(round(random.uniform(75, 99), 1))

        # Generate anomalies (random for now)
        anomalies = []
        if anomaly_detection != "none":
            # Add 2-3 random anomalies
            num_anomalies = random.randint(2, 3)
            for _ in range(num_anomalies):
                index = random.randint(0, len(time_labels) - 1)
                value = kpi_data[index]

                # Determine expected range
                if kpi_type == "oee":
                    expected_min = 75
                    expected_max = 85
                elif kpi_type == "mttr":
                    expected_min = 2
                    expected_max = 10
                elif kpi_type == "mtbf":
                    expected_min = 200
                    expected_max = 400
                elif kpi_type == "availability":
                    expected_min = 80
                    expected_max = 95
                elif kpi_type == "performance":
                    expected_min = 75
                    expected_max = 90
                elif kpi_type == "quality":
                    expected_min = 85
                    expected_max = 98

                # Calculate deviation
                if value < expected_min:
                    deviation = value - expected_min
                    severity = "high" if abs(deviation) > (expected_min * 0.2) else "medium"
                elif value > expected_max:
                    deviation = value - expected_max
                    severity = "medium"  # High values are usually not as critical
                else:
                    continue  # Skip if within expected range

                anomalies.append({
                    "index": index,
                    "date": time_labels[index],
                    "value": value,
                    "expected_range": [expected_min, expected_max],
                    "deviation": deviation,
                    "severity": severity
                })

        # Calculate summary statistics
        current_value = kpi_data[-1] if kpi_data else 0
        average_value = sum(kpi_data) / len(kpi_data) if kpi_data else 0

        # Set target based on KPI type
        if kpi_type == "oee":
            target_value = 90.0
        elif kpi_type == "mttr":
            target_value = 4.0
        elif kpi_type == "mtbf":
            target_value = 400.0
        elif kpi_type == "availability":
            target_value = 95.0
        elif kpi_type == "performance":
            target_value = 90.0
        elif kpi_type == "quality":
            target_value = 99.0
        else:
            target_value = 0

        # Calculate trend (change from previous period)
        if len(kpi_data) >= 2:
            if kpi_type in ["oee", "mtbf", "availability", "performance", "quality"]:
                # For these KPIs, higher is better
                trend = kpi_data[-1] - kpi_data[-2]
            else:
                # For MTTR, lower is better
                trend = kpi_data[-2] - kpi_data[-1]
        else:
            trend = 0

        # Calculate gap to target
        if kpi_type in ["oee", "mtbf", "availability", "performance", "quality"]:
            # For these KPIs, higher is better
            gap = target_value - current_value
        else:
            # For MTTR, lower is better
            gap = current_value - target_value

        # Return the complete data structure
        return {
            "timeLabels": time_labels,
            "kpiData": kpi_data,
            "benchmarkData": benchmark_data,
            "anomalies": anomalies,
            "summary": {
                "currentValue": current_value,
                "averageValue": average_value,
                "targetValue": target_value,
                "trend": trend,
                "gap": gap,
                "anomalyCount": len(anomalies)
            }
        }
    except Exception as e:
        print(f"Error fetching KPI analytics data: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error fetching KPI analytics data: {str(e)}")

# --- Dashboard Widget Endpoints ---
@app.get("/api/assets/count", tags=["Dashboard"])
async def get_assets_count(db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get the total number of assets. Any authenticated user can view.
    """
    try:
        # Count assets
        count = db.query(func.count(Asset.assetid)).scalar()

        return {"count": count}
    except Exception as e:
        print(f"Error fetching asset count: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching asset count: {str(e)}")

@app.get("/api/inventory/summary", tags=["Dashboard"])
async def get_inventory_summary(db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get inventory summary data. Any authenticated user can view.
    """
    try:
        # Get all parts
        parts = db.query(Sparepart).all()

        # Calculate total value
        total_value = sum(part.stockquantity * part.unitprice for part in parts if part.stockquantity is not None and part.unitprice is not None)

        # Calculate total quantity
        total_quantity = sum(part.stockquantity for part in parts if part.stockquantity is not None)

        # Count parts below reorder level
        below_reorder = sum(1 for part in parts if part.stockquantity is not None and part.reorderlevel is not None and part.stockquantity <= part.reorderlevel)

        return {
            "total_value": float(total_value),
            "total_quantity": total_quantity,
            "below_reorder": below_reorder,
            "total_parts": len(parts)
        }
    except Exception as e:
        print(f"Error fetching inventory summary: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching inventory summary: {str(e)}")

@app.get("/api/workorders/count", tags=["Dashboard"])
async def get_workorders_count(status: Optional[str] = None, db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get the count of work orders, optionally filtered by status. Any authenticated user can view.
    """
    try:
        # Build query
        query = db.query(func.count(WorkOrder.workorderid))

        # Apply status filter if provided
        if status:
            query = query.filter(WorkOrder.status == status)

        # Execute query
        count = query.scalar()

        return {"count": count}
    except Exception as e:
        print(f"Error fetching work order count: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching work order count: {str(e)}")

# --- ADD Inventory Optimization Endpoints ---
@app.get("/inventory/optimization-report", response_class=HTMLResponse, tags=["Inventory"])
async def inventory_optimization_report_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Inventory Optimization Report page.
    """
    try:
        return templates.TemplateResponse("inventory_optimization_report.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Inventory Optimization Report page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Inventory Optimization Report page: {str(e)}")

@app.get("/api/inventory/optimization-report", tags=["Inventory"])
async def get_inventory_optimization_report(db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get inventory optimization report data. Any authenticated user can view.
    """
    try:
        # Get all parts with analysis data
        subquery = db.query(
            InventoryAnalysis.part_id,
            func.max(InventoryAnalysis.analysis_date).label('latest_date')
        ).group_by(InventoryAnalysis.part_id).subquery('latest_analysis')

        analyses = db.query(InventoryAnalysis).join(
            subquery,
            and_(
                InventoryAnalysis.part_id == subquery.c.part_id,
                InventoryAnalysis.analysis_date == subquery.c.latest_date
            )
        ).all()

        # Get all parts
        parts = {}
        for part in db.query(Sparepart).all():
            parts[part.partid] = part

        # Get all recommendations
        recommendations = db.query(InventoryRecommendation).filter(
            InventoryRecommendation.status == 'NEW'
        ).order_by(asc(InventoryRecommendation.priority)).all()

        # Calculate summary metrics
        total_parts = len(parts)
        total_value = sum(part.stockquantity * part.unitprice for part in parts.values() if part.stockquantity is not None and part.unitprice is not None)

        # Calculate optimal value and potential savings
        optimal_value = 0
        potential_savings = 0
        for analysis in analyses:
            optimal_value += analysis.optimal_cost
            if analysis.potential_savings > 0:
                potential_savings += analysis.potential_savings

        # Calculate inventory status counts
        stockout_risk_count = 0
        optimal_stock_count = 0
        overstock_count = 0

        for analysis in analyses:
            if analysis.stockout_risk > 20:
                stockout_risk_count += 1
            elif analysis.stock_difference > 0:
                overstock_count += 1
            else:
                optimal_stock_count += 1

        # Prepare savings opportunities data
        savings_data = []
        for analysis in analyses:
            if analysis.potential_savings > 0:
                part = parts.get(analysis.part_id)
                if part:
                    savings_data.append({
                        "part_id": analysis.part_id,
                        "part_name": part.partname,
                        "current_stock": analysis.current_stock,
                        "optimal_stock": analysis.optimal_stock,
                        "stock_difference": analysis.stock_difference,
                        "current_cost": float(analysis.current_cost),
                        "optimal_cost": float(analysis.optimal_cost),
                        "potential_savings": float(analysis.potential_savings),
                        "abc_classification": part.abc_classification
                    })

        # Sort by potential savings (descending)
        savings_data.sort(key=lambda x: x["potential_savings"], reverse=True)

        # Limit to top 10
        savings_data = savings_data[:10]

        # Prepare stockout risk data
        stockout_risk_data = []
        for analysis in analyses:
            if analysis.stockout_risk > 20:
                part = parts.get(analysis.part_id)
                if part:
                    stockout_risk_data.append({
                        "part_id": analysis.part_id,
                        "part_name": part.partname,
                        "current_stock": analysis.current_stock,
                        "reorder_point": float(part.reorder_point) if part.reorder_point else 0,
                        "days_of_supply": analysis.days_of_supply,
                        "lead_time_days": part.leadtimedays,
                        "stockout_risk": float(analysis.stockout_risk),
                        "abc_classification": part.abc_classification
                    })

        # Sort by stockout risk (descending)
        stockout_risk_data.sort(key=lambda x: x["stockout_risk"], reverse=True)

        # Limit to top 10
        stockout_risk_data = stockout_risk_data[:10]

        # Prepare recommendations data
        recommendations_data = []
        for rec in recommendations:
            part = parts.get(rec.part_id)
            if part:
                recommendations_data.append({
                    "part_id": rec.part_id,
                    "part_name": part.partname,
                    "recommendation_type": rec.recommendation_type,
                    "reason": rec.reason,
                    "priority": rec.priority
                })

        # Limit to top 10
        recommendations_data = recommendations_data[:10]

        # Prepare ABC analysis data
        abc_analysis = {
            "A": {"count": 0, "value": 0},
            "B": {"count": 0, "value": 0},
            "C": {"count": 0, "value": 0},
            "Unclassified": {"count": 0, "value": 0}
        }

        for part in parts.values():
            classification = part.abc_classification or "Unclassified"
            value = part.stockquantity * part.unitprice if part.stockquantity is not None and part.unitprice is not None else 0

            abc_analysis[classification]["count"] += 1
            abc_analysis[classification]["value"] += float(value)

        # Prepare response
        return {
            "summary": {
                "total_parts": total_parts,
                "total_value": float(total_value),
                "optimal_value": float(optimal_value),
                "potential_savings": float(potential_savings)
            },
            "status": {
                "stockout_risk_count": stockout_risk_count,
                "optimal_stock_count": optimal_stock_count,
                "overstock_count": overstock_count
            },
            "savings": savings_data,
            "stockout_risk": stockout_risk_data,
            "recommendations": recommendations_data,
            "abc_analysis": abc_analysis
        }
    except Exception as e:
        print(f"Error generating inventory optimization report: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating inventory optimization report: {str(e)}")

@app.get("/inventory/config/eoq", response_class=HTMLResponse, tags=["Inventory"])
async def inventory_config_eoq_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the EOQ Configuration page. Requires ADMIN role for editing.
    """
    try:
        return templates.TemplateResponse("inventory_config_eoq.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering EOQ Configuration page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering EOQ Configuration page: {str(e)}")

@app.get("/api/inventory/eoq-summary", tags=["Inventory"])
async def get_eoq_summary(db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get summary of EOQ calculations. Any authenticated user can view.
    """
    try:
        # Get total number of parts
        total_parts = db.query(func.count(Sparepart.partid)).scalar()

        # Get number of parts with EOQ
        parts_with_eoq = db.query(func.count(Sparepart.partid)).filter(Sparepart.eoq.isnot(None)).scalar()

        return {
            "total_parts": total_parts,
            "parts_with_eoq": parts_with_eoq
        }
    except Exception as e:
        print(f"Error fetching EOQ summary: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching EOQ summary: {str(e)}")

@app.get("/api/inventory/parts", tags=["Inventory"], response_model=Dict[str, Any])
async def get_inventory_parts(
    request: Request,
    limit: int = Query(10, ge=1, le=100, description="Number of items to return per page"),
    offset: int = Query(0, ge=0, description="Number of items to skip"),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    db: Session = Depends(get_db),
    api_key: Optional[str] = Depends(get_api_key),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """
    Get all spare parts with pagination and filtering. Any authenticated user can view.
    """
    # Check if either API key or user authentication is provided
    if not api_key and not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # If using user authentication, check if user has required role
    if current_user and current_user.role not in [UserRole.VIEWER, UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions",
        )

    try:
        # Start with a base query
        query = db.query(Sparepart)

        # Apply filters from query parameters
        filter_params = {k: v for k, v in request.query_params.items()
                        if k not in ["limit", "offset", "sort_by", "sort_order"]}

        if filter_params:
            query = apply_filters_from_params(query, filter_params, Sparepart)

        # Apply sorting
        if sort_by and hasattr(Sparepart, sort_by):
            sort_column = getattr(Sparepart, sort_by)
            if sort_order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
        else:
            # Default sorting
            query = query.order_by(asc(Sparepart.partname))

        # Get total count before pagination
        total = query.count()

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute query
        parts = query.all()

        # Convert to list of dictionaries
        parts_list = [
            {
                "partid": part.partid,
                "partname": part.partname,
                "partnumber": part.partnumber,
                "manufacturer": part.manufacturer,
                "stockquantity": part.stockquantity,
                "reorderlevel": part.reorderlevel,
                "unitprice": float(part.unitprice) if part.unitprice else None,
                "leadtimedays": part.leadtimedays,
                "eoq": float(part.eoq) if part.eoq else None,
                "calculated_safety_stock": float(part.calculated_safety_stock) if part.calculated_safety_stock else None,
                "abc_classification": part.abc_classification,
                "lastrestocked": part.lastrestocked,
                "ordering_cost": float(part.ordering_cost) if part.ordering_cost else None,
                "holding_cost_percent": float(part.holding_cost_percent) if part.holding_cost_percent else None
            } for part in parts
        ]

        # Calculate pagination metadata
        has_more = (offset + limit) < total

        # Generate links for next and previous pages
        base_url = str(request.url).split('?')[0]
        query_params = dict(request.query_params)

        # Next page link
        next_page = None
        if has_more:
            next_offset = offset + limit
            query_params['offset'] = str(next_offset)
            next_page = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in query_params.items()])}"

        # Previous page link
        prev_page = None
        if offset > 0:
            prev_offset = max(0, offset - limit)
            query_params['offset'] = str(prev_offset)
            prev_page = f"{base_url}?{'&'.join([f'{k}={v}' for k, v in query_params.items()])}"

        return {
            "items": parts_list,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": has_more,
            "next_page": next_page,
            "prev_page": prev_page
        }
    except Exception as e:
        print(f"Error fetching inventory parts: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching inventory parts: {str(e)}")

@app.get("/api/inventory/parts/{part_id}", tags=["Inventory"])
async def get_inventory_part(
    part_id: int,
    db: Session = Depends(get_db),
    api_key: Optional[str] = Depends(get_api_key),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    # Check if either API key or user authentication is provided
    if not api_key and not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # If using user authentication, check if user has required role
    if current_user and current_user.role not in [UserRole.VIEWER, UserRole.ENGINEER, UserRole.MANAGER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions",
        )
    """
    Get a specific spare part. Any authenticated user can view.
    """
    try:
        # Get the part
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if not part:
            raise HTTPException(status_code=404, detail=f"Part with ID {part_id} not found")

        # Convert to dictionary
        part_dict = {
            "partid": part.partid,
            "partname": part.partname,
            "partnumber": part.partnumber,
            "manufacturer": part.manufacturer,
            "stockquantity": part.stockquantity,
            "reorderlevel": part.reorderlevel,
            "unitprice": float(part.unitprice) if part.unitprice else None,
            "leadtimedays": part.leadtimedays,
            "eoq": float(part.eoq) if part.eoq else None,
            "calculated_safety_stock": float(part.calculated_safety_stock) if part.calculated_safety_stock else None,
            "abc_classification": part.abc_classification,
            "lastrestocked": part.lastrestocked,
            "ordering_cost": float(part.ordering_cost) if part.ordering_cost else None,
            "holding_cost_percent": float(part.holding_cost_percent) if part.holding_cost_percent else None
        }

        return part_dict
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error fetching inventory part: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching inventory part: {str(e)}")

@app.put("/api/inventory/parts/{part_id}/eoq-params", tags=["Inventory"])
async def update_part_eoq_params(
    part_id: int,
    params: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Update EOQ parameters for a specific part. Requires ADMIN role.
    """
    try:
        # Get the part
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if not part:
            raise HTTPException(status_code=404, detail=f"Part with ID {part_id} not found")

        # Update parameters
        if "ordering_cost" in params:
            part.ordering_cost = Decimal(str(params["ordering_cost"]))

        if "holding_cost_percent" in params:
            part.holding_cost_percent = Decimal(str(params["holding_cost_percent"]))

        # Save changes
        db.commit()

        return {"status": "success", "message": f"EOQ parameters for part {part_id} updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error updating part EOQ parameters: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating part EOQ parameters: {str(e)}")

@app.delete("/api/inventory/parts/{part_id}/eoq-params", tags=["Inventory"])
async def delete_part_eoq_params(
    part_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Delete EOQ parameters for a specific part. Requires ADMIN role.
    """
    try:
        # Get the part
        part = db.query(Sparepart).filter(Sparepart.partid == part_id).first()
        if not part:
            raise HTTPException(status_code=404, detail=f"Part with ID {part_id} not found")

        # Reset parameters
        part.ordering_cost = None
        part.holding_cost_percent = None

        # Save changes
        db.commit()

        return {"status": "success", "message": f"EOQ parameters for part {part_id} deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error deleting part EOQ parameters: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting part EOQ parameters: {str(e)}")

@app.get("/api/inventory/run-optimization", tags=["Inventory"])
async def run_inventory_optimization(db: Session = Depends(get_db), current_user: User = Depends(require_manager_plus)):
    """
    Manually trigger the inventory optimization job. Requires MANAGER or ADMIN role.
    """
    try:
        # First, update inventory metrics to calculate EOQ and safety stock
        update_inventory_metrics_job()

        # Then run the optimization job
        run_inventory_optimization_job()

        return {"status": "success", "message": "Inventory optimization job completed successfully."}
    except Exception as e:
        print(f"Error running inventory optimization job: {e}")
        raise HTTPException(status_code=500, detail=f"Error running inventory optimization job: {str(e)}")

# --- Chart Examples Page ---
@app.get("/chart-examples", response_class=HTMLResponse, tags=["Visualization"])
async def chart_examples_page(request: Request, current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Chart Examples page demonstrating the chart utility functions.
    """
    try:
        return templates.TemplateResponse("chart_examples.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Chart Examples page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Chart Examples page: {str(e)}")

# --- KPI Dashboard Page ---
@app.get("/kpi-dashboard", response_class=HTMLResponse, tags=["Analytics"])
async def kpi_dashboard_page(request: Request):
    """
    Serves the unified KPI Dashboard page with improved visualizations.
    """
    try:
        return templates.TemplateResponse("kpi_dashboard.html", {
            "request": request,
            "current_user": None
        })
    except Exception as e:
        print(f"Error rendering KPI Dashboard page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering KPI Dashboard page: {str(e)}")

# --- ADD Inventory Configuration Endpoints ---
@app.get("/api/inventory/config", tags=["Inventory"], response_model=List[dict])
async def get_inventory_config(db: Session = Depends(get_db), current_user: User = Depends(require_viewer_plus)):
    """
    Get all inventory configuration parameters. Any authenticated user can view.
    """
    try:
        # Get all config parameters from the database
        config_params = db.query(InventoryConfig).all()

        # Convert to list of dictionaries
        config_list = [
            {
                "id": param.id,
                "parameter_name": param.parameter_name,
                "parameter_value": param.parameter_value,
                "parameter_description": param.parameter_description,
                "parameter_type": param.parameter_type,
                "updated_at": param.updated_at
            } for param in config_params
        ]

        return config_list
    except Exception as e:
        print(f"Error fetching inventory configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Error fetching inventory configuration: {str(e)}")

@app.post("/api/inventory/config", tags=["Inventory"], status_code=201)
async def create_inventory_config(config_data: InventoryConfigCreate, db: Session = Depends(get_db), current_user: User = Depends(require_admin)):
    """
    Create a new inventory configuration parameter. Requires ADMIN role.
    """
    try:
        # Check if parameter already exists
        existing_param = db.query(InventoryConfig).filter(InventoryConfig.parameter_name == config_data.parameter_name).first()
        if existing_param:
            raise HTTPException(status_code=400, detail=f"Parameter '{config_data.parameter_name}' already exists")

        # Create new parameter
        new_param = InventoryConfig(
            parameter_name=config_data.parameter_name,
            parameter_value=config_data.parameter_value,
            parameter_description=config_data.parameter_description,
            parameter_type=config_data.parameter_type
        )

        db.add(new_param)
        db.commit()
        db.refresh(new_param)

        return {
            "id": new_param.id,
            "parameter_name": new_param.parameter_name,
            "parameter_value": new_param.parameter_value,
            "parameter_description": new_param.parameter_description,
            "parameter_type": new_param.parameter_type,
            "created_at": new_param.created_at
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"Error creating inventory configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating inventory configuration: {str(e)}")

@app.put("/api/inventory/config/{param_id}", tags=["Inventory"])
async def update_inventory_config(
    config_data: InventoryConfigUpdate,
    param_id: int = Path(..., title="The ID of the parameter to update", ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Update an existing inventory configuration parameter. Requires ADMIN role.
    """
    try:
        # Get parameter
        param = db.query(InventoryConfig).filter(InventoryConfig.id == param_id).first()
        if not param:
            raise HTTPException(status_code=404, detail=f"Parameter with ID {param_id} not found")

        # Update parameter
        param.parameter_value = config_data.parameter_value
        if config_data.parameter_description is not None:
            param.parameter_description = config_data.parameter_description

        db.commit()
        db.refresh(param)

        return {
            "id": param.id,
            "parameter_name": param.parameter_name,
            "parameter_value": param.parameter_value,
            "parameter_description": param.parameter_description,
            "parameter_type": param.parameter_type,
            "updated_at": param.updated_at
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"Error updating inventory configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating inventory configuration: {str(e)}")

@app.delete("/api/inventory/config/{param_id}", tags=["Inventory"])
async def delete_inventory_config(
    param_id: int = Path(..., title="The ID of the parameter to delete", ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Delete an inventory configuration parameter. Requires ADMIN role.
    """
    try:
        # Get parameter
        param = db.query(InventoryConfig).filter(InventoryConfig.id == param_id).first()
        if not param:
            raise HTTPException(status_code=404, detail=f"Parameter with ID {param_id} not found")

        # Delete parameter
        db.delete(param)
        db.commit()

        return {"message": f"Parameter '{param.parameter_name}' deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"Error deleting inventory configuration: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting inventory configuration: {str(e)}")

# --- END Inventory Configuration Endpoints ---

# --- Bulk Operations for Inventory Parts ---
@app.post("/api/inventory/parts/bulk", tags=["Inventory"], response_model=BulkOperationResponse)
async def bulk_create_parts(
    request: BulkCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager_plus)
):
    """
    Bulk create multiple spare parts. Requires MANAGER or ADMIN role.
    """
    try:
        # Use the bulk_create utility
        result = bulk_create(db, Sparepart, request.items)

        return BulkOperationResponse(
            status="success",
            message=f"Successfully created {len(result)} spare parts",
            success_count=len(result)
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Error in bulk create parts: {e}")
        raise HTTPException(status_code=500, detail=f"Error in bulk create parts: {str(e)}")

@app.put("/api/inventory/parts/bulk", tags=["Inventory"], response_model=BulkOperationResponse)
async def bulk_update_parts(
    request: BulkUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager_plus)
):
    """
    Bulk update multiple spare parts. Requires MANAGER or ADMIN role.
    """
    try:
        # Use the bulk_update utility
        result = bulk_update(db, Sparepart, request.items, id_field="partid")

        return BulkOperationResponse(
            status="success",
            message=f"Successfully updated {len(result)} spare parts",
            success_count=len(result)
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Error in bulk update parts: {e}")
        raise HTTPException(status_code=500, detail=f"Error in bulk update parts: {str(e)}")

@app.delete("/api/inventory/parts/bulk", tags=["Inventory"], response_model=BulkOperationResponse)
async def bulk_delete_parts(
    request: BulkDeleteRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_manager_plus)
):
    """
    Bulk delete multiple spare parts. Requires MANAGER or ADMIN role.
    """
    try:
        # Use the bulk_delete utility
        result = bulk_delete(db, Sparepart, request.ids, id_field="partid")

        return BulkOperationResponse(
            status="success",
            message=f"Successfully deleted {result['deleted_count']} spare parts",
            success_count=result['deleted_count']
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Error in bulk delete parts: {e}")
        raise HTTPException(status_code=500, detail=f"Error in bulk delete parts: {str(e)}")

# --- END Bulk Operations for Inventory Parts ---

@app.get("/api/users", tags=["Users"], response_model=List[dict])
async def get_users(db: Session = Depends(get_db), current_user: User = Depends(require_admin)):
    """
    Get all users. Requires ADMIN role.
    """
    try:
        # Log the admin's request
        print(f"INFO: User list requested by admin {current_user.email}")

        # Get all users from the database
        users = db.query(User).all()

        # Convert to list of dictionaries (excluding sensitive data)
        user_list = [
            {
                "user_id": user.user_id,
                "email": user.email,
                "role": user.role,
                "full_name": user.full_name,
                "created_at": user.created_at,
                "last_login": user.last_login
            } for user in users
        ]

        return user_list
    except Exception as e:
        print(f"ERROR: Failed to fetch users: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch users")

@app.get("/api/users/me", tags=["Users"])
async def get_current_user_profile(current_user: User = Depends(get_current_user)):
    """
    Get the current user's profile information.
    Requires authentication but no specific role.
    """
    try:
        # Update last login time
        db = SessionLocal()
        try:
            db_user = db.query(User).filter(User.user_id == current_user.user_id).first()
            if db_user:
                db_user.last_login = datetime.now()
                db.commit()
        except Exception as e:
            print(f"WARNING: Failed to update last login time: {e}")
            db.rollback()
        finally:
            db.close()

        # Return user profile
        return {
            "user_id": current_user.user_id,
            "email": current_user.email,
            "role": current_user.role,
            "full_name": current_user.full_name,
            "created_at": current_user.created_at,
            "last_login": current_user.last_login
        }
    except Exception as e:
        print(f"ERROR: Failed to fetch user profile: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch user profile")
# User Permissions Management Endpoints
@app.get("/api/users/{user_id}/permissions", tags=["Users"])
async def get_user_permissions(user_id: str, db: Session = Depends(get_db), current_user: User = Depends(require_admin)):
    """
    Get permissions for a specific user. Requires ADMIN role.
    """
    try:
        # Get user
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail=f"User with ID {user_id} not found")

        # Get user permissions
        user_permissions = db.query(UserPermission).filter(UserPermission.user_id == user_id).all()
        permissions = [p.permission for p in user_permissions]

        return {
            "user_id": user.user_id,
            "email": user.email,
            "role": user.role,
            "api_access_enabled": user.api_access_enabled,
            "api_key_id": user.api_key_id,
            "permissions": permissions
        }
    except HTTPException:
        raise
    except Exception as e:
        print(f"ERROR: Failed to fetch user permissions: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch user permissions")

@app.put("/api/users/{user_id}/permissions", tags=["Users"])
async def update_user_permissions(
    user_id: str,
    permissions_data: UserPermissionsUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Update permissions for a specific user. Requires ADMIN role.
    """
    try:
        # Get user
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail=f"User with ID {user_id} not found")

        # Update API access
        user.api_access_enabled = permissions_data.api_access_enabled

        # Update permissions
        # Delete existing permissions
        db.query(UserPermission).filter(UserPermission.user_id == user_id).delete()

        # Add new permissions
        for permission in permissions_data.permissions:
            db.add(UserPermission(user_id=user_id, permission=permission))

        db.commit()

        return {"message": "User permissions updated successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"ERROR: Failed to update user permissions: {e}")
        raise HTTPException(status_code=500, detail="Failed to update user permissions")

@app.post("/api/users/{user_id}/api-key", tags=["Users"])
async def generate_api_key(user_id: str, db: Session = Depends(get_db), current_user: User = Depends(require_admin)):
    """
    Generate a new API key for a specific user. Requires ADMIN role.
    """
    try:
        # Get user
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail=f"User with ID {user_id} not found")

        # Check if API access is enabled
        if not user.api_access_enabled:
            raise HTTPException(status_code=400, detail="API access is not enabled for this user")

        # Generate API key
        import uuid
        import secrets

        # Generate a secure random API key
        api_key = secrets.token_hex(32)  # 64 character hex string

        # Generate a unique ID for the API key
        api_key_id = str(uuid.uuid4())

        # Hash the API key for storage
        # In a production system, you would use a more secure hashing algorithm
        # and add a salt, but this is sufficient for this example
        # api_key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        # We're not storing the hash in this implementation, but in a real system
        # you would store the hash in a separate table for API key validation

        # Update user with the API key hash
        user.api_key_id = api_key_id

        # In a real system, you would store the hash in a separate table
        # along with the API key ID, but for simplicity we're just storing the ID
        # in the user table

        db.commit()

        # Return the API key to the user - this is the only time they will see it
        return {"api_key_id": api_key_id, "api_key": api_key}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"ERROR: Failed to generate API key: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate API key")

@app.delete("/api/users/{user_id}/api-key", tags=["Users"])
async def revoke_api_key(user_id: str, db: Session = Depends(get_db), current_user: User = Depends(require_admin)):
    """
    Revoke the API key for a specific user. Requires ADMIN role.
    """
    try:
        # Get user
        user = db.query(User).filter(User.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail=f"User with ID {user_id} not found")

        # Check if user has an API key
        if not user.api_key_id:
            raise HTTPException(status_code=400, detail="User does not have an API key")

        # Revoke API key
        user.api_key_id = None
        db.commit()

        return {"message": "API key revoked successfully"}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"ERROR: Failed to revoke API key: {e}")
        raise HTTPException(status_code=500, detail="Failed to revoke API key")

@app.get("/user-permissions", response_class=HTMLResponse, tags=["Users"])
async def user_permissions_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the User Permissions Management page. Requires ADMIN role.
    """
    try:
        # Check if user is authenticated and has admin role
        if not current_user or current_user.role != UserRole.ADMIN:
            return RedirectResponse(url="/login?next=/user-permissions", status_code=303)

        # Get all users
        users = db.query(User).all()

        return templates.TemplateResponse("user_permissions.html", {
            "request": request,
            "current_user": current_user,
            "users": users
        })
    except Exception as e:
        print(f"Error rendering User Permissions page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering User Permissions page: {str(e)}")

# --- END User Management Endpoints ---

# --- Usage Analytics Page ---
@app.get("/usage-analytics", response_class=HTMLResponse, tags=["Analytics"])
async def usage_analytics_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Usage Analytics page. Requires ADMIN role for viewing.
    """
    try:
        # Check if user is authenticated and has admin role
        if not current_user or current_user.role != UserRole.ADMIN:
            return RedirectResponse(url="/login?next=/usage-analytics", status_code=303)

        return templates.TemplateResponse("usage_analytics.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Usage Analytics page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Usage Analytics page: {str(e)}")

# --- Initialize Usage Analytics Routes ---
try:
    from routes.usage_analytics import init_router, usage_analytics_router  # type: ignore
    init_router(get_db, require_permission_func=lambda permission: require_admin)
    app.include_router(usage_analytics_router)
    print("INFO:     Usage analytics routes initialized successfully.")

    # Register usage reporting scheduled jobs
    try:
        from jobs.usage_reporting import register_scheduled_jobs
        register_scheduled_jobs()
        print("INFO:     Usage reporting scheduled jobs registered successfully.")
    except ImportError as e:
        print(f"WARNING:  Could not import usage reporting jobs: {e}")
        print("WARNING:  Scheduled usage reports will not be generated automatically.")
except ImportError as e:
    print(f"WARNING:  Could not import usage analytics routes: {e}")
    print("WARNING:  Usage analytics API endpoints will not be available.")

# --- Authentication Debug Endpoints ---

@app.get("/firebase-login-test", response_class=HTMLResponse, tags=["Authentication"])
async def firebase_login_test_page(request: Request):
    """
    Serves a test page for Firebase Authentication.
    """
    return templates.TemplateResponse("firebase_login_test.html", {"request": request})

@app.get("/auth-tests", response_class=HTMLResponse, tags=["Authentication"])
async def auth_tests_page(request: Request):
    """
    Serves a test page for authentication testing.
    """
    return templates.TemplateResponse("auth_tests.html", {"request": request})

@app.get("/api/user-check/{user_id}", tags=["Authentication"])
async def user_check(user_id: str, db: Session = Depends(get_db)):
    """
    Check if a user with the given user_id exists in the database.
    """
    try:
        # Get user information from the database
        print(f"Querying database for user with user_id: {user_id}")
        user = db.query(User).filter(User.user_id == user_id).first()

        if user:
            print(f"Found user in database: {user.email}")
            # Return user information
            user_info = {
                "user_id": user.user_id,
                "email": user.email,
                "role": user.role,
                "full_name": user.full_name
            }
            print(f"Returning user info: {user_info}")
            return user_info
        else:
            print(f"User with user_id {user_id} not found in database")
            # Let's check what users are in the database
            all_users = db.query(User).limit(5).all()
            print(f"Sample users in database: {[u.user_id for u in all_users]}")

            return JSONResponse(
                status_code=404,
                content={"error": f"User with user_id {user_id} not found in database"}
            )
    except Exception as e:
        print(f"Error checking user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error checking user: {str(e)}"}
        )

@app.post("/api/debug-token", tags=["Authentication"])
async def debug_token(request: Request):
    """
    Debug endpoint for token verification.
    """
    print("=== Debug Token Request ===")
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        print(f"Authorization header: {auth_header[:20]}..." if auth_header else "No Authorization header")

        if not auth_header or not auth_header.startswith('Bearer '):
            print("No valid authorization header provided")
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]
        print(f"Token extracted: {token[:20]}...")

        # Verify the token with Firebase Admin SDK
        try:
            print("Attempting to verify token with Firebase Admin SDK...")
            decoded_token = auth.verify_id_token(token)
            firebase_uid = decoded_token.get("uid")
            if not firebase_uid:
                print("ERROR: UID not found in decoded token")
                return JSONResponse(
                    status_code=401,
                    content={"error": "UID not found in decoded token"}
                )

            print(f"Successfully verified token for UID: {firebase_uid}")
            print(f"Decoded token claims: {decoded_token}")

            # Check if user exists in database
            db = SessionLocal()
            try:
                # Query the users table for the user with this UID
                print(f"Querying database for user with UID: {firebase_uid}")
                user = db.query(User).filter(User.user_id == firebase_uid).first()

                if user:
                    print(f"Found user in database: {user.email}")
                    user_exists = True
                    user_info = {
                        "user_id": user.user_id,
                        "email": user.email,
                        "role": user.role,
                        "full_name": user.full_name
                    }
                else:
                    print(f"User with UID {firebase_uid} not found in database")
                    # Let's check what users are in the database
                    all_users = db.query(User).limit(5).all()
                    print(f"Sample users in database: {[u.user_id for u in all_users]}")
                    user_exists = False
                    user_info = None
            finally:
                db.close()

            # Return the decoded token
            return {
                "success": True,
                "uid": firebase_uid,
                "decoded_token": {k: v for k, v in decoded_token.items() if k not in ['iat', 'exp', 'aud', 'iss']},
                "user_exists_in_db": user_exists,
                "user_info": user_info
            }
        except Exception as e:
            print(f"Firebase token verification failed: {e}")
            print(f"Token that failed verification: {token[:30]}...")
            import traceback
            traceback.print_exc()
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )


    except Exception as e:
        print(f"Error debugging token: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=401,
            content={"error": f"Invalid token: {str(e)}"}
        )

@app.get("/api/user/profile", tags=["User"])
async def get_user_profile(current_user: User = Depends(get_current_user)):
    """
    Get the current user's profile information.
    """
    return {
        "user_id": current_user.user_id,
        "email": current_user.email,
        "role": current_user.role.value,
        "full_name": current_user.full_name,
        "created_at": current_user.created_at.isoformat() if current_user.created_at else None
    }

@app.post("/api/register", tags=["Authentication"], status_code=201)
async def register_user(request: Request, db: Session = Depends(get_db)):
    """
    Register a new user in the database using Firebase authentication.
    This endpoint automatically adds a new user to the database if they are authenticated with Firebase.
    """
    try:
        # Get the token from the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JSONResponse(
                status_code=401,
                content={"error": "No valid authorization header provided"}
            )

        token = auth_header.split(' ')[1]

        # Verify the token with Firebase Admin SDK
        try:
            decoded_token = auth.verify_id_token(token)
            firebase_uid = decoded_token.get("uid")
            if not firebase_uid:
                return JSONResponse(
                    status_code=401,
                    content={"error": "UID not found in decoded token"}
                )

            # Get user information from the token
            email = decoded_token.get("email")
            name = decoded_token.get("name") or decoded_token.get("email").split('@')[0]

            if not email:
                return JSONResponse(
                    status_code=400,
                    content={"error": "Email not found in token"}
                )

            # Check if user already exists in database
            user = db.query(User).filter(User.user_id == firebase_uid).first()
            if user:
                return JSONResponse(
                    status_code=409,
                    content={
                        "message": "User already exists",
                        "user_id": user.user_id,
                        "email": user.email,
                        "role": user.role
                    }
                )

            # Create new user with VIEWER role by default
            new_user = User(
                user_id=firebase_uid,
                email=email,
                role=UserRole.VIEWER,  # Default role is VIEWER
                full_name=name,
                created_at=datetime.now(),
                api_access_enabled=True
            )

            db.add(new_user)
            db.commit()
            db.refresh(new_user)

            print(f"Successfully registered new user: {new_user.email} with role {new_user.role}")

            return {
                "success": True,
                "message": "User registered successfully",
                "user": {
                    "user_id": new_user.user_id,
                    "email": new_user.email,
                    "role": new_user.role,
                    "full_name": new_user.full_name
                }
            }

        except Exception as e:
            print(f"Firebase token verification failed during registration: {e}")
            import traceback
            traceback.print_exc()
            return JSONResponse(
                status_code=401,
                content={"error": f"Firebase token verification failed: {str(e)}"}
            )
    except Exception as e:
        print(f"Error registering user: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": f"Error registering user: {str(e)}"}
        )


# --- Webhooks Page ---
@app.get("/webhooks", response_class=HTMLResponse, tags=["Integration"])
async def webhooks_page(request: Request, db: Session = Depends(get_db), current_user: Optional[User] = Depends(get_current_user_optional)):
    """
    Serves the Webhooks page. Requires MANAGER or ADMIN role for viewing.
    """
    try:
        # Check if user is authenticated and has manager or admin role
        if not current_user or (current_user.role != UserRole.MANAGER and current_user.role != UserRole.ADMIN):
            return RedirectResponse(url="/login?next=/webhooks", status_code=303)

        return templates.TemplateResponse("webhooks.html", {
            "request": request,
            "current_user": current_user
        })
    except Exception as e:
        print(f"Error rendering Webhooks page: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error rendering Webhooks page: {str(e)}")

# --- Initialize Webhook Routes ---
try:
    from routes.webhooks import init_router, webhooks_router  # type: ignore
    init_router(get_db, require_admin, require_manager_plus, require_viewer_plus)
    app.include_router(webhooks_router)
    print("INFO:     Webhook routes initialized successfully.")
except ImportError as e:
    print(f"WARNING:  Could not import webhook routes: {e}")
    print("WARNING:  Webhook API endpoints will not be available.")

# --- To run the app (from terminal) ---
# Ensure .env file exists with DATABASE_URL and SECRET_API_KEY
# Install dependencies: pip install fastapi uvicorn sqlalchemy psycopg2-binary pandas apscheduler python-dotenv pydantic Jinja2 python-multipart
# Create requirements.txt: pip freeze > requirements.txt

# Run: python -m uvicorn main:app --reload